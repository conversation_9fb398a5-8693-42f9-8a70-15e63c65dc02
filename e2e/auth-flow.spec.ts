import { expect, test } from "@playwright/test";

test.describe("Authentication Flow E2E", () => {
  test.beforeEach(async ({ page }) => {
    // Start from the home page
    await page.goto("/");
  });

  test("should display login page correctly", async ({ page }) => {
    await page.goto("/login");

    // Check page title and heading
    await expect(page).toHaveTitle(/Planty Invoice/);
    await expect(page.getByText("Welcome to Planty Invoice")).toBeVisible();
    await expect(
      page.getByText("Sign in to your account to manage your invoices"),
    ).toBeVisible();

    // Check form elements
    await expect(page.getByLabel("Email")).toBeVisible();
    await expect(page.getByLabel("Password")).toBeVisible();
    await expect(page.getByRole("button", { name: "Sign In" })).toBeVisible();
    await expect(page.getByRole("button", { name: "Create Account" }))
      .toBeVisible();

    // Check back link
    await expect(page.getByText("← Back to Home")).toBeVisible();
  });

  test("should show error for invalid email format", async ({ page }) => {
    await page.goto("/login?error=invalid-email");

    await expect(page.getByText("Please enter a valid email address."))
      .toBeVisible();
    await expect(page.getByRole("alert")).toBeVisible();
  });

  test("should show error for invalid credentials", async ({ page }) => {
    await page.goto("/login?error=invalid-credentials");

    await expect(
      page.getByText(
        "Invalid email or password. Please check your credentials and try again.",
      ),
    ).toBeVisible();
    await expect(page.getByRole("alert")).toBeVisible();
  });

  test("should show success message for email confirmation", async ({ page }) => {
    await page.goto("/login?message=check-email");

    await expect(
      page.getByText(
        "Check your email for a confirmation link to complete your account setup.",
      ),
    ).toBeVisible();
  });

  test("should redirect to login when accessing protected routes without authentication", async ({ page }) => {
    await page.goto("/invoices");

    // Should redirect to login page
    await expect(page).toHaveURL("/login");
    await expect(page.getByText("Welcome to Planty Invoice")).toBeVisible();
  });

  test("should redirect to login when accessing manage-store without authentication", async ({ page }) => {
    await page.goto("/manage-store");

    // Should redirect to login page
    await expect(page).toHaveURL("/login");
    await expect(page.getByText("Welcome to Planty Invoice")).toBeVisible();
  });

  test("should show appropriate home page content for unauthenticated user", async ({ page }) => {
    await page.goto("/");

    // Check for unauthenticated content
    await expect(page.getByText("Get Started")).toBeVisible();
    await expect(page.getByText("Sign In to Start")).toBeVisible();

    // Check navigation
    await expect(page.getByText("Sign In")).toBeVisible();
    await expect(page.queryByText("Sign Out")).not.toBeVisible();
  });

  test("should have proper form validation attributes", async ({ page }) => {
    await page.goto("/login");

    const emailInput = page.getByLabel("Email");
    const passwordInput = page.getByLabel("Password");

    // Check input attributes
    await expect(emailInput).toHaveAttribute("type", "email");
    await expect(emailInput).toHaveAttribute("required");
    await expect(emailInput).toHaveAttribute("autocomplete", "email");

    await expect(passwordInput).toHaveAttribute("type", "password");
    await expect(passwordInput).toHaveAttribute("required");
    await expect(passwordInput).toHaveAttribute(
      "autocomplete",
      "current-password",
    );
  });

  test("should navigate between pages correctly", async ({ page }) => {
    // Start at home
    await page.goto("/");
    await expect(page.getByText("Planty Invoice")).toBeVisible();

    // Go to login
    await page.getByText("Sign In").click();
    await expect(page).toHaveURL("/login");

    // Go back to home
    await page.getByText("← Back to Home").click();
    await expect(page).toHaveURL("/");
  });

  test("should have responsive design", async ({ page }) => {
    await page.goto("/login");

    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.getByText("Welcome to Planty Invoice")).toBeVisible();

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.getByText("Welcome to Planty Invoice")).toBeVisible();
    await expect(page.getByLabel("Email")).toBeVisible();
    await expect(page.getByLabel("Password")).toBeVisible();
  });

  test("should handle theme switching", async ({ page }) => {
    await page.goto("/");

    // The theme switcher should be present
    // This test might need adjustment based on the actual ThemeSwitcher implementation
    const body = page.locator("body");
    await expect(body).toBeVisible();
  });

  test("should have proper accessibility features", async ({ page }) => {
    await page.goto("/login");

    // Check for proper labeling
    await expect(page.getByLabel("Email")).toBeVisible();
    await expect(page.getByLabel("Password")).toBeVisible();

    // Check for proper button roles
    await expect(page.getByRole("button", { name: "Sign In" })).toBeVisible();
    await expect(page.getByRole("button", { name: "Create Account" }))
      .toBeVisible();

    // Check for alert role when error is present
    await page.goto("/login?error=invalid-credentials");
    await expect(page.getByRole("alert")).toBeVisible();
  });

  test("should handle keyboard navigation", async ({ page }) => {
    await page.goto("/login");

    // Tab through form elements
    await page.keyboard.press("Tab");
    await expect(page.getByLabel("Email")).toBeFocused();

    await page.keyboard.press("Tab");
    await expect(page.getByLabel("Password")).toBeFocused();

    await page.keyboard.press("Tab");
    await expect(page.getByRole("button", { name: "Sign In" })).toBeFocused();
  });

  test("should display all error message types correctly", async ({ page }) => {
    const errorCases = [
      {
        error: "missing-credentials",
        message: "Please enter both email and password.",
      },
      {
        error: "email-not-confirmed",
        message:
          "Please check your email and click the confirmation link before signing in.",
      },
      {
        error: "too-many-requests",
        message:
          "Too many login attempts. Please wait a moment before trying again.",
      },
      {
        error: "user-not-found",
        message: "No account found with this email address.",
      },
      {
        error: "password-too-short",
        message: "Password must be at least 6 characters long.",
      },
      {
        error: "user-already-exists",
        message:
          "An account with this email already exists. Please sign in instead.",
      },
      {
        error: "signup-disabled",
        message: "New user registration is currently disabled.",
      },
      {
        error: "unknown-error",
        message: "An unexpected error occurred. Please try again.",
      },
    ];

    for (const { error, message } of errorCases) {
      await page.goto(`/login?error=${error}`);
      await expect(page.getByText(message)).toBeVisible();
      await expect(page.getByRole("alert")).toBeVisible();
    }
  });

  test("should handle navigation between different sections", async ({ page }) => {
    await page.goto("/");

    // Test navigation links
    const homeLink = page.getByText("Home").first();
    const invoicesLink = page.getByText("Invoices").first();

    await expect(homeLink).toBeVisible();
    await expect(invoicesLink).toBeVisible();

    // Click invoices (should redirect to login)
    await invoicesLink.click();
    await expect(page).toHaveURL("/login");
  });

  test("should handle mobile navigation", async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto("/");

    // Mobile navigation should be visible
    const mobileNav = page.locator('[role="navigation"]').last();
    await expect(mobileNav).toBeVisible();

    // Should have mobile-specific navigation items
    await expect(page.getByText("Home")).toBeVisible();
    await expect(page.getByText("Invoices")).toBeVisible();
  });
});
