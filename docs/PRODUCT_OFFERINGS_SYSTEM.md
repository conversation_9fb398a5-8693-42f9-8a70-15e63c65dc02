# Product Offerings System

## Overview

The Product Offerings System connects creators to the existing products and product_types tables, allowing creators to manage their own inventory, pricing, and product availability. This system enables stores to browse and order from creator catalogs while giving creators full control over their offerings.

## Database Schema

### New Table: `user_product_offerings`

This table connects user profiles (creators) to products, allowing creators to:

- Set stock quantities and minimum stock levels
- Mark products as available/discontinued
- Override default pricing with custom prices
- Feature specific products
- Add internal notes and set display order

```sql
CREATE TABLE user_product_offerings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_profile_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,

    -- Inventory management
    stock_quantity INTEGER DEFAULT 0 NOT NULL,
    min_stock_level INTEGER DEFAULT 0 NOT NULL,

    -- Availability control
    is_available BOOLEAN DEFAULT true NOT NULL,
    is_featured BOOLEAN DEFAULT false NOT NULL,

    -- Pricing (optional override of product_type price)
    custom_price DECIMAL(10,2),

    -- Creator-specific details
    creator_notes TEXT,
    display_order INTEGER DEFAULT 0,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    UNIQUE(user_profile_id, product_id),
    CONSTRAINT check_creator_only CHECK (
        user_profile_id IN (
            SELECT id FROM user_profiles WHERE user_type = 'creator'
        )
    )
);
```

### Views

#### `public_product_listings`

A comprehensive view for public browsing that shows:

- Available products from active creators
- Calculated effective pricing (custom or base price)
- Stock status indicators
- Creator information
- Filtered to show only available items

#### `creator_product_offerings`

A management view for creators that includes:

- All offerings (available and unavailable)
- Private creator notes
- Full product and creator details
- Stock and availability status

### Database Functions

#### `get_current_user_product_offerings()`

Returns all product offerings for the authenticated creator user.

#### `update_offering_stock(offering_id, new_quantity)`

Safely updates stock quantity for a creator's offering.

#### `toggle_offering_availability(offering_id)`

Toggles the availability status of a creator's offering.

#### `check_product_availability(creator_id, product_id)`

Checks if a specific product is available from a creator.

#### `get_featured_products(limit)`

Returns featured products across all creators for homepage display.

## Row Level Security (RLS)

### Public Access

- **Read**: Anyone can view available offerings through `public_product_listings`
- **Products**: Public read access to active products and all product types

### Creator Access

- **Read**: Creators can view all their own offerings (including unavailable ones)
- **Write**: Creators can insert, update, and delete only their own offerings
- **Products**: Creators can view all products to create new offerings

### Security Features

- Only users with `user_type = 'creator'` can create offerings
- Creators can only manage their own offerings
- Public access is limited to available items only

## TypeScript Types

All types are derived from the database schema using TypeScript utility types like `Pick<>`, `Omit<>`, and `Partial<>` to ensure tight coupling with the database and automatic updates when the schema changes.

### Core Types (Derived from Database Schema)

```typescript
// Base table types from database schema
export type UserProductOffering =
  DatabaseTables["user_product_offerings"]["Row"];
export type UserProductOfferingInsert =
  DatabaseTables["user_product_offerings"]["Insert"];
export type UserProductOfferingUpdate =
  DatabaseTables["user_product_offerings"]["Update"];

// View types derived from database schema
export type PublicProductListing =
  DatabaseViews["public_product_listings"]["Row"];
export type CreatorProductOffering =
  DatabaseViews["creator_product_offerings"]["Row"];

// Function return types derived from database schema
export type GetCurrentUserProductOfferingsResult =
  DatabaseFunctions["get_current_user_product_offerings"]["Returns"][0];
export type CheckProductAvailabilityResult =
  DatabaseFunctions["check_product_availability"]["Returns"][0];
export type GetFeaturedProductsResult =
  DatabaseFunctions["get_featured_products"]["Returns"][0];
```

### Form Types (Using TypeScript Utility Types)

```typescript
// Form data types using TypeScript utility types
export type CreateOfferingData = Omit<
  UserProductOfferingInsert,
  "user_profile_id"
>;
export type UpdateOfferingData = UserProductOfferingUpdate;

// Stock management types derived from view return types
export type StockStatus = NonNullable<PublicProductListing["stock_status"]>;
export type AvailabilityStatus = NonNullable<
  PublicProductListing["availability_status"]
>;
```

### Advanced Utility Types

```typescript
// Filtering types using Pick and Partial
export type OfferingFilters = Partial<
  Pick<UserProductOffering, "is_available" | "is_featured">
> & {
  stock_status?: StockStatus;
  availability_status?: AvailabilityStatus;
  creator_id?: UserProductOffering["user_profile_id"];
  product_type_id?: Product["product_type_id"];
};

// Sorting options using keyof and Pick
export type OfferingSortOptions = {
  field: keyof Pick<
    PublicProductListing,
    | "product_name"
    | "creator_name"
    | "effective_price"
    | "stock_quantity"
    | "display_order"
  >;
  direction: "asc" | "desc";
};
```

## Usage Examples

### For Creators

#### Creating a New Offering

```typescript
const newOffering: CreateOfferingData = {
  product_id: "product-uuid",
  stock_quantity: 50,
  min_stock_level: 10,
  is_available: true,
  is_featured: false,
  custom_price: 25.99, // Override default price
  display_order: 1,
};
```

#### Managing Stock

```typescript
// Update stock quantity
await supabase.rpc("update_offering_stock", {
  offering_id_param: "offering-uuid",
  new_quantity: 25,
});

// Toggle availability
await supabase.rpc("toggle_offering_availability", {
  offering_id_param: "offering-uuid",
});
```

### For Stores

#### Browsing Available Products

```typescript
const { data: products } = await supabase
  .from("public_product_listings")
  .select("*")
  .eq("availability_status", "available")
  .order("is_featured", { ascending: false })
  .order("creator_name");
```

#### Checking Product Availability

```typescript
const { data: availability } = await supabase.rpc(
  "check_product_availability",
  {
    creator_id_param: "creator-uuid",
    product_id_param: "product-uuid",
  }
);
```

## Migration Files

1. `20250812_create_user_product_offerings.sql` - Creates the main table
2. `20250812_user_product_offerings_rls.sql` - Sets up RLS policies
3. `20250812_create_product_listings_view.sql` - Creates views
4. `20250812_create_product_offering_functions.sql` - Creates helper functions

## Next Steps

To implement this system:

1. **Database setup is complete** - All tables, views, functions, and RLS policies have been applied directly to the database.

2. **Regenerate database types** (when needed):

   ```bash
   npm run gen:types
   ```

   **⚠️ IMPORTANT**: Never manually edit `lib/types/database.ts` - it's auto-generated by the `npm run gen:types` script and any changes will be overwritten.

3. Use the provided TypeScript types for type-safe operations
4. Implement UI components for creator product management
5. Create store browsing interfaces using the public views

## Benefits

- **Scalable**: Each creator manages their own inventory independently
- **Flexible**: Custom pricing and availability control per creator
- **Secure**: RLS ensures data isolation and proper access control
- **Type-safe**: Comprehensive TypeScript types for all operations
- **Performance**: Optimized indexes and views for common queries
