import { createClient } from '@/lib/supabase/server'
import { Navigation, MobileNavigation } from './navigation'

export async function NavigationWrapper() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  return <Navigation user={user} />
}

export async function MobileNavigationWrapper() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  return <MobileNavigation user={user} />
}
