"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CreateProductData, ProductType } from "@/lib/types/database";
import { getProductTypes } from "@/lib/services/database";

interface ProductFormProps {
  onSubmit: (data: CreateProductData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export function ProductForm({
  onSubmit,
  onCancel,
  isLoading = false,
}: ProductFormProps) {
  const [formData, setFormData] = useState<CreateProductData>({
    product_type_id: "",
  });
  const [productTypes, setProductTypes] = useState<ProductType[]>([]);
  const [loadingTypes, setLoadingTypes] = useState(true);

  useEffect(() => {
    loadProductTypes();
  }, []);

  const loadProductTypes = async () => {
    setLoadingTypes(true);
    const result = await getProductTypes();
    if (result.data) {
      setProductTypes(result.data);
    }
    setLoadingTypes(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(formData);
  };

  const handleInputChange = (field: keyof CreateProductData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Card className="w-full max-w-lg">
      <CardHeader>
        <CardTitle>Add New Product Variant</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="product_type_id">Product Type *</Label>
            <Select
              value={formData.product_type_id}
              onValueChange={(value: string) =>
                handleInputChange("product_type_id", value)
              }
              disabled={loadingTypes}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={
                    loadingTypes ? "Loading..." : "Select product type"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {productTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    <div className="flex flex-col">
                      <span>{type.name}</span>
                      <span className="text-xs text-muted-foreground">
                        €{type.price.toFixed(2)}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="sku">SKU</Label>
            <Input
              id="sku"
              value={formData.sku || ""}
              onChange={(e) => handleInputChange("sku", e.target.value)}
              placeholder="Product SKU (optional)"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Input
                id="color"
                value={formData.color || ""}
                onChange={(e) => handleInputChange("color", e.target.value)}
                placeholder="e.g., Red, Blue"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="scent">Scent</Label>
              <Input
                id="scent"
                value={formData.scent || ""}
                onChange={(e) => handleInputChange("scent", e.target.value)}
                placeholder="e.g., Vanilla, Lavender"
              />
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isLoading || !formData.product_type_id}
            >
              {isLoading ? "Creating..." : "Create Product"}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
