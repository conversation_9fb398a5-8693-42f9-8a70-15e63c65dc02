"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { ProductWithType } from "@/lib/types/database";
import { getProducts } from "@/lib/services/database";
import { Plus } from "lucide-react";

interface ProductSelectorProps {
  value?: string;
  onValueChange: (productId: string, product?: ProductWithType) => void;
  onAddNew?: () => void;
  label?: string;
}

export function ProductSelector({
  value,
  onValueChange,
  onAddNew,
  label = "Product",
}: ProductSelectorProps) {
  const [products, setProducts] = useState<ProductWithType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    setIsLoading(true);
    setError(null);

    const result = await getProducts();
    if (result.error) {
      setError(result.error);
    } else {
      setProducts(result.data || []);
    }

    setIsLoading(false);
  };

  const getProductDisplayName = (product: ProductWithType) => {
    const parts = [product.product_type?.name];
    if (product.color) parts.push(product.color);
    if (product.scent) parts.push(product.scent);
    return parts.filter(Boolean).join(" - ");
  };

  const handleValueChange = (productId: string) => {
    const selectedProduct = products.find((p) => p.id === productId);
    onValueChange(productId, selectedProduct);
  };

  if (error) {
    return (
      <div className="space-y-2">
        <Label>{label}</Label>
        <div className="text-red-500 text-sm">
          Error loading products: {error}
        </div>
        <Button onClick={loadProducts} variant="outline" size="sm">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="product">{label}</Label>
      <div className="flex gap-2">
        <Select
          value={value}
          onValueChange={handleValueChange}
          disabled={isLoading}
        >
          <SelectTrigger className="flex-1">
            <SelectValue
              placeholder={
                isLoading ? "Loading products..." : "Select a product"
              }
            />
          </SelectTrigger>
          <SelectContent>
            {products.map((product) => (
              <SelectItem key={product.id} value={product.id}>
                <div className="flex flex-col">
                  <span>{getProductDisplayName(product)}</span>
                  <div className="flex gap-2 text-xs text-muted-foreground">
                    {product.sku && <span>SKU: {product.sku}</span>}
                    {product.product_type?.price && (
                      <span>€{product.product_type.price.toFixed(2)}</span>
                    )}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {onAddNew && (
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={onAddNew}
          >
            <Plus className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
