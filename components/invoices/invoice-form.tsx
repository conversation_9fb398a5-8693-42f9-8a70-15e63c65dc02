"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CustomerSelector } from "@/components/customers/customer-selector";
import { InvoiceItemRow } from "./invoice-item-row";
import {
  InvoiceFormData,
  InvoiceItemFormData,
  SalesChannel,
  PaymentMethod,
} from "@/lib/types/database";
import { Plus } from "lucide-react";

interface InvoiceFormProps {
  onSubmit: (data: InvoiceFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

const SALES_CHANNELS: { value: SalesChannel; label: string }[] = [
  { value: "in_store", label: "In Store" },
  { value: "website", label: "Website" },
  { value: "partner_store", label: "Partner Store" },
];

const PAYMENT_METHODS: { value: PaymentMethod; label: string }[] = [
  { value: "cash", label: "Cash" },
  { value: "card", label: "Card" },
  { value: "bank_transfer", label: "Bank Transfer" },
  { value: "shopify", label: "Shopify" },
];

export function InvoiceForm({
  onSubmit,
  onCancel,
  isLoading = false,
}: InvoiceFormProps) {
  const [formData, setFormData] = useState<InvoiceFormData>({
    customer_id: "",
    invoice_date: new Date().toISOString().split("T")[0],
    sales_channel: "in_store",
    shipping_amount: 0,
    vat_percentage: 21,
    items: [
      {
        product_id: "",
        description: "",
        unit_price: 0,
        quantity: 1,
        line_total: 0,
      },
    ],
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Convert to InvoiceFormData format
    const invoiceData: InvoiceFormData = {
      customer_id: formData.customer_id,
      invoice_date: formData.invoice_date,
      due_date: formData.due_date,
      sales_channel: formData.sales_channel,
      payment_method: formData.payment_method,
      shipping_amount: formData.shipping_amount,
      vat_percentage: formData.vat_percentage,
      notes: formData.notes,
      items: formData.items.map((item) => ({
        product_id: item.product_id,
        description: item.description,
        unit_price: item.unit_price,
        quantity: item.quantity,
        line_total: item.line_total,
      })),
    };

    await onSubmit(invoiceData);
  };

  const handleInputChange = (field: keyof InvoiceFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemUpdate = (
    index: number,
    updatedItem: InvoiceItemFormData
  ) => {
    const newItems = [...formData.items];
    newItems[index] = updatedItem;
    setFormData((prev) => ({
      ...prev,
      items: newItems,
    }));
  };

  const handleItemRemove = (index: number) => {
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, i) => i !== index);
      setFormData((prev) => ({
        ...prev,
        items: newItems,
      }));
    }
  };

  const handleAddItem = () => {
    const newItem: InvoiceItemFormData = {
      product_id: "",
      description: "",
      unit_price: 0,
      quantity: 1,
      line_total: 0,
    };
    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  // Calculate totals
  const subtotal = formData.items.reduce(
    (sum, item) => sum + item.line_total,
    0
  );
  const vatAmount = (subtotal * (formData.vat_percentage || 21)) / 100;
  const total = subtotal + vatAmount + (formData.shipping_amount || 0);

  const isFormValid =
    formData.customer_id &&
    formData.items.every(
      (item) => item.product_id && item.description && item.quantity > 0
    );

  return (
    <Card className="w-full max-w-6xl">
      <CardHeader>
        <CardTitle>Create New Invoice</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Customer and Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <CustomerSelector
              value={formData.customer_id}
              onValueChange={(customerId) =>
                handleInputChange("customer_id", customerId)
              }
            />

            <div className="space-y-2">
              <Label htmlFor="sales_channel">Sales Channel</Label>
              <Select
                value={formData.sales_channel}
                onValueChange={(value: SalesChannel) =>
                  handleInputChange("sales_channel", value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SALES_CHANNELS.map((channel) => (
                    <SelectItem key={channel.value} value={channel.value}>
                      {channel.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="invoice_date">Invoice Date</Label>
              <Input
                id="invoice_date"
                type="date"
                value={formData.invoice_date}
                onChange={(e) =>
                  handleInputChange("invoice_date", e.target.value)
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="due_date">Due Date</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date || ""}
                onChange={(e) => handleInputChange("due_date", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="payment_method">Payment Method</Label>
              <Select
                value={formData.payment_method || ""}
                onValueChange={(value: PaymentMethod) =>
                  handleInputChange("payment_method", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_METHODS.map((method) => (
                    <SelectItem key={method.value} value={method.value}>
                      {method.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Invoice Items */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Invoice Items</h3>
              <Button
                type="button"
                onClick={handleAddItem}
                variant="outline"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>

            <div className="space-y-2">
              {formData.items.map((item, index) => (
                <InvoiceItemRow
                  key={index}
                  item={item}
                  onUpdate={(updatedItem) =>
                    handleItemUpdate(index, updatedItem)
                  }
                  onRemove={() => handleItemRemove(index)}
                  canRemove={formData.items.length > 1}
                />
              ))}
            </div>
          </div>

          {/* Totals and Additional Info */}
          <div className="grid grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shipping_amount">Shipping Amount (€)</Label>
                  <Input
                    id="shipping_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.shipping_amount}
                    onChange={(e) =>
                      handleInputChange(
                        "shipping_amount",
                        parseFloat(e.target.value) || 0
                      )
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="vat_percentage">VAT Percentage (%)</Label>
                  <Input
                    id="vat_percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={formData.vat_percentage}
                    onChange={(e) =>
                      handleInputChange(
                        "vat_percentage",
                        parseFloat(e.target.value) || 0
                      )
                    }
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes || ""}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                  placeholder="Additional notes..."
                />
              </div>
            </div>

            {/* Totals Summary */}
            <div className="space-y-2 p-4 bg-muted rounded-lg">
              <h4 className="font-semibold">Invoice Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>€{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping:</span>
                  <span>€{(formData.shipping_amount || 0).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>VAT ({formData.vat_percentage}%):</span>
                  <span>€{vatAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-semibold text-base border-t pt-1">
                  <span>Total:</span>
                  <span>€{total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <Button type="submit" disabled={isLoading || !isFormValid}>
              {isLoading ? "Creating..." : "Create Invoice"}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
