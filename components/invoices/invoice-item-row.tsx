"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ProductSelector } from "@/components/products/product-selector";
import { InvoiceItemFormData, ProductWithType } from "@/lib/types/database";
import { Trash2 } from "lucide-react";

interface InvoiceItemRowProps {
  item: InvoiceItemFormData;
  onUpdate: (item: InvoiceItemFormData) => void;
  onRemove: () => void;
  canRemove?: boolean;
}

export function InvoiceItemRow({
  item,
  onUpdate,
  onRemove,
  canRemove = true,
}: InvoiceItemRowProps) {
  const [selectedProduct, setSelectedProduct] = useState<
    ProductWithType | undefined
  >();

  const handleProductChange = (
    productId: string,
    product?: ProductWithType
  ) => {
    setSelectedProduct(product);

    const updatedItem: InvoiceItemFormData = {
      ...item,
      product_id: productId,
      description: product?.product_type?.name || item.description,
      unit_price: product?.product_type?.price || item.unit_price,
      line_total:
        (product?.product_type?.price || item.unit_price) * item.quantity,
    };

    onUpdate(updatedItem);
  };

  const handleQuantityChange = (quantity: number) => {
    const updatedItem: InvoiceItemFormData = {
      ...item,
      quantity,
      line_total: item.unit_price * quantity,
    };
    onUpdate(updatedItem);
  };

  const handleUnitPriceChange = (unit_price: number) => {
    const updatedItem: InvoiceItemFormData = {
      ...item,
      unit_price,
      line_total: unit_price * item.quantity,
    };
    onUpdate(updatedItem);
  };

  const handleDescriptionChange = (description: string) => {
    const updatedItem: InvoiceItemFormData = {
      ...item,
      description,
    };
    onUpdate(updatedItem);
  };

  return (
    <div className="grid grid-cols-12 gap-2 items-end p-4 border rounded-lg">
      {/* Product Selector */}
      <div className="col-span-4">
        <ProductSelector
          value={item.product_id}
          onValueChange={handleProductChange}
          label="Product"
        />
      </div>

      {/* Description */}
      <div className="col-span-3">
        <label className="text-sm font-medium">Description</label>
        <Input
          value={item.description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          placeholder="Item description"
        />
      </div>

      {/* Quantity */}
      <div className="col-span-1">
        <label className="text-sm font-medium">Qty</label>
        <Input
          type="number"
          min="1"
          value={item.quantity}
          onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
        />
      </div>

      {/* Unit Price */}
      <div className="col-span-2">
        <label className="text-sm font-medium">Unit Price (€)</label>
        <Input
          type="number"
          step="0.01"
          min="0"
          value={item.unit_price}
          onChange={(e) =>
            handleUnitPriceChange(parseFloat(e.target.value) || 0)
          }
        />
      </div>

      {/* Line Total */}
      <div className="col-span-1">
        <label className="text-sm font-medium">Total</label>
        <div className="h-10 flex items-center px-3 bg-muted rounded-md text-sm">
          €{item.line_total.toFixed(2)}
        </div>
      </div>

      {/* Remove Button */}
      <div className="col-span-1">
        {canRemove && (
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={onRemove}
            className="text-red-500 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
