/**
 * TypeScript types for product offerings and related functionality
 *
 * This file defines types for the creator product offering system, allowing
 * creators to manage their product inventory, pricing, and availability.
 *
 * Key features:
 * - Product offerings management for creators
 * - Stock level tracking and availability control
 * - Custom pricing overrides
 * - Public product listings for stores to browse
 * - Type-safe database operations
 *
 * All types are derived from the database schema using TypeScript utility types
 * like Pick<>, Omit<>, and Partial<> to ensure tight coupling with the database.
 */

import { Database } from "./database";

// Extract database table types
type DatabaseTables = Database["public"]["Tables"];
type DatabaseViews = Database["public"]["Views"];
type DatabaseFunctions = Database["public"]["Functions"];

// Base table types from database schema
export type Product = DatabaseTables["products"]["Row"];
export type ProductType = DatabaseTables["product_types"]["Row"];

// User product offerings table type - derived from database schema
export type UserProductOffering =
  DatabaseTables["user_product_offerings"]["Row"];

// Insert and Update types for user product offerings - derived from database schema
export type UserProductOfferingInsert =
  DatabaseTables["user_product_offerings"]["Insert"];
export type UserProductOfferingUpdate =
  DatabaseTables["user_product_offerings"]["Update"];

// View types derived from database schema
export type PublicProductListing =
  DatabaseViews["public_product_listings"]["Row"];
export type CreatorProductOffering =
  DatabaseViews["creator_product_offerings"]["Row"];

// Form data types for creating/updating offerings
export type CreateOfferingData =
  & Omit<
    UserProductOfferingInsert,
    "user_profile_id"
  >
  & {
    product_id: string;
  };

export type UpdateOfferingData = UserProductOfferingUpdate;

// Stock management types - derived from the database view return types
export type StockStatus = NonNullable<PublicProductListing["stock_status"]>;
export type AvailabilityStatus = NonNullable<
  PublicProductListing["availability_status"]
>;

// Function return types derived from database schema
export type GetCurrentUserProductOfferingsResult =
  DatabaseFunctions["get_current_user_product_offerings"]["Returns"][0];

export type CheckProductAvailabilityResult =
  DatabaseFunctions["check_product_availability"]["Returns"][0];

export type GetFeaturedProductsResult =
  DatabaseFunctions["get_featured_products"]["Returns"][0];

// Branded ID types for type safety
export type ProductId = string & { readonly brand: unique symbol };
export type ProductTypeId = string & { readonly brand: unique symbol };
export type OfferingId = string & { readonly brand: unique symbol };

// Type-safe versions using branded IDs
export type TypeSafeProduct = Omit<Product, "id" | "product_type_id"> & {
  id: ProductId;
  product_type_id: ProductTypeId;
};

export type TypeSafeProductType = Omit<ProductType, "id"> & {
  id: ProductTypeId;
};

export type TypeSafeUserProductOffering =
  & Omit<UserProductOffering, "id" | "product_id">
  & {
    id: OfferingId;
    product_id: ProductId;
  };

// Helper types for form validation
export type OfferingFormData =
  & Required<
    Pick<CreateOfferingData, "product_id" | "stock_quantity">
  >
  & Partial<Omit<CreateOfferingData, "product_id" | "stock_quantity">>;

// Type guards for runtime type checking
export const isStockStatus = (value: string): value is StockStatus => {
  return ["out_of_stock", "low_stock", "in_stock"].includes(value);
};

export const isAvailabilityStatus = (
  value: string,
): value is AvailabilityStatus => {
  return ["discontinued", "product_inactive", "out_of_stock", "available"]
    .includes(value);
};

export const isValidOffering = (
  data: Partial<UserProductOffering>,
): data is Required<
  Pick<UserProductOffering, "user_profile_id" | "product_id">
> => {
  return !!(data.user_profile_id && data.product_id);
};

// Utility types for filtering and sorting using TypeScript utility types
export type OfferingFilters =
  & Partial<
    Pick<
      UserProductOffering,
      "is_available" | "is_featured"
    >
  >
  & {
    stock_status?: StockStatus;
    availability_status?: AvailabilityStatus;
    creator_id?: UserProductOffering["user_profile_id"];
    product_type_id?: Product["product_type_id"];
  };

export type OfferingSortOptions = {
  field: keyof Pick<
    PublicProductListing,
    | "product_name"
    | "creator_name"
    | "effective_price"
    | "stock_quantity"
    | "display_order"
  >;
  direction: "asc" | "desc";
};

// Combined types for complex operations using intersection types
export type OfferingWithProduct = UserProductOffering & {
  product: Product;
  product_type: ProductType;
};

export type PublicListingWithCreator = PublicProductListing & {
  creator:
    & Pick<
      PublicProductListing,
      | "creator_id"
      | "creator_name"
      | "creator_logo_url"
      | "creator_contact_email"
    >
    & {
      id: PublicProductListing["creator_id"];
      name: PublicProductListing["creator_name"];
      logo_url: PublicProductListing["creator_logo_url"];
      contact_email: PublicProductListing["creator_contact_email"];
    };
};
