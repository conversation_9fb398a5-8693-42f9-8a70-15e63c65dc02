/**
 * TypeScript types for user profiles and related tables
 *
 * This file demonstrates advanced TypeScript patterns by leveraging the database schema
 * types from database.ts instead of hardcoding interface definitions. This approach:
 *
 * 1. Ensures type safety between database schema and application code
 * 2. Automatically updates types when database schema changes
 * 3. Reduces code duplication and maintenance overhead
 * 4. Leverages TypeScript utility types for flexible type composition
 *
 * Key TypeScript features used:
 * - Pick<T, K>: Select specific properties from a type
 * - Omit<T, K>: Exclude specific properties from a type
 * - Partial<T>: Make all properties optional
 * - Required<T>: Make all properties required
 * - Conditional types: T extends U ? X : Y
 * - Mapped types: { [K in keyof T]: ... }
 * - Type guards: value is Type
 * - Intersection types: A & B
 * - Template literal types and key extraction
 */

import { Database } from "./database";

// Extract database table types
type DatabaseTables = Database["public"]["Tables"];
type DatabaseViews = Database["public"]["Views"];
type DatabaseEnums = Database["public"]["Enums"];

// Base table types from database schema
export type Address = DatabaseTables["addresses"]["Row"];
export type UserProfile = DatabaseTables["user_profiles"]["Row"];
export type Shop = DatabaseTables["shops"]["Row"];

// Extract the user type enum from database
export type UserType = DatabaseEnums["user_type_enum"];

// View types from database schema (these already include the combined data)
export type UserProfileWithBilling =
  DatabaseViews["user_profiles_with_billing"]["Row"];
export type ShopWithAddress = DatabaseViews["shops_with_addresses"]["Row"];

// Form data types using database Insert/Update types
export type CreateAddressData = Omit<
  DatabaseTables["addresses"]["Insert"],
  "id" | "created_at" | "updated_at"
>;

export type UpdateAddressData = Omit<
  DatabaseTables["addresses"]["Update"],
  "id" | "created_at" | "updated_at"
>;

export type UpdateUserProfileData = Omit<
  DatabaseTables["user_profiles"]["Update"],
  "id" | "user_id" | "created_at" | "updated_at"
>;

export type CreateShopData = Omit<
  DatabaseTables["shops"]["Insert"],
  "id" | "store_id" | "created_at" | "updated_at"
>;

export type UpdateShopData = Omit<
  DatabaseTables["shops"]["Update"],
  "id" | "store_id" | "created_at" | "updated_at"
>;

// Generic API response type
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

// Specific API response types using the generic type
export type UserProfileResponse = ApiResponse<UserProfileWithBilling>;
export type ShopsResponse = ApiResponse<ShopWithAddress[]>;
export type AddressResponse = ApiResponse<Address>;
export type ShopResponse = ApiResponse<ShopWithAddress>;

// Additional utility types for common operations
export type AddressFields = keyof Address;
export type UserProfileFields = keyof UserProfile;
export type ShopFields = keyof Shop;

// Partial types for optional updates
export type PartialAddress = Partial<Address>;
export type PartialUserProfile = Partial<UserProfile>;
export type PartialShop = Partial<Shop>;

// Required fields for creation (excluding auto-generated fields)
export type RequiredAddressFields = Pick<
  Address,
  "nickname" | "address_line_1" | "city" | "country"
>;
export type RequiredUserProfileFields = Pick<
  UserProfile,
  "user_id" | "store_name" | "user_type"
>;
export type RequiredShopFields = Pick<Shop, "store_id" | "shop_nickname">;

// Database operation types using the actual database schema types
export type AddressInsert = DatabaseTables["addresses"]["Insert"];
export type AddressUpdate = DatabaseTables["addresses"]["Update"];
export type UserProfileInsert = DatabaseTables["user_profiles"]["Insert"];
export type UserProfileUpdate = DatabaseTables["user_profiles"]["Update"];
export type ShopInsert = DatabaseTables["shops"]["Insert"];
export type ShopUpdate = DatabaseTables["shops"]["Update"];

// Function return types from database functions
export type GetCurrentUserShopsResult =
  Database["public"]["Functions"]["get_current_user_shops"]["Returns"];

// Re-export product offering types for convenience
export type {
  AvailabilityStatus,
  CheckProductAvailabilityResult,
  CreateOfferingData,
  CreatorProductOffering,
  GetCurrentUserProductOfferingsResult,
  GetFeaturedProductsResult,
  PublicProductListing,
  StockStatus,
  UpdateOfferingData,
  UserProductOffering,
} from "./product-offerings";

// Conditional types for nullable fields
export type NonNullable<T> = T extends null | undefined ? never : T;
export type NullableFields<T> = {
  [K in keyof T]: T[K] extends null | undefined ? K : never;
}[keyof T];
export type RequiredFields<T> = {
  [K in keyof T]: T[K] extends null | undefined ? never : K;
}[keyof T];

// Helper types for form validation
export type AddressFormData = Required<CreateAddressData>;
export type UserProfileFormData =
  & Required<
    Pick<UpdateUserProfileData, "store_name" | "user_type">
  >
  & Partial<Omit<UpdateUserProfileData, "store_name" | "user_type">>;
export type ShopFormData =
  & Required<Pick<CreateShopData, "shop_nickname">>
  & Partial<Omit<CreateShopData, "shop_nickname">>;

// Type guards for runtime type checking
export const isUserType = (value: string): value is UserType => {
  return ["creator", "store"].includes(value);
};

export const isValidAddress = (
  data: Partial<Address>,
): data is RequiredAddressFields => {
  return !!(
    data.nickname &&
    data.address_line_1 &&
    data.city &&
    data.country
  );
};

// Advanced utility types for complex operations
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

// Extract string literal types from database enums
export type UserTypeValues = `${UserType}`;

// Create a union of all possible field names across all tables
export type AllTableFields = AddressFields | UserProfileFields | ShopFields;

// Utility type to extract only string fields from a type
export type StringFields<T> = {
  [K in keyof T]: T[K] extends string ? K : never;
}[keyof T];

// Utility type to extract only optional fields from a type
export type OptionalFields<T> = {
  [K in keyof T]: T[K] extends undefined ? K : never;
}[keyof T];

// Create branded types for IDs to prevent mixing different ID types
export type AddressId = string & { readonly __brand: "AddressId" };
export type UserProfileId = string & { readonly __brand: "UserProfileId" };
export type ShopId = string & { readonly __brand: "ShopId" };
export type UserId = string & { readonly __brand: "UserId" };

// Helper functions to create branded IDs
export const createAddressId = (id: string): AddressId => id as AddressId;
export const createUserProfileId = (id: string): UserProfileId =>
  id as UserProfileId;
export const createShopId = (id: string): ShopId => id as ShopId;
export const createUserId = (id: string): UserId => id as UserId;

// Type-safe versions of our main types using branded IDs
export type TypeSafeAddress = Omit<Address, "id"> & { id: AddressId };
export type TypeSafeUserProfile =
  & Omit<UserProfile, "id" | "user_id" | "billing_address_id">
  & {
    id: UserProfileId;
    user_id: UserId;
    billing_address_id: AddressId | null;
  };
export type TypeSafeShop = Omit<Shop, "id" | "store_id" | "address_id"> & {
  id: ShopId;
  store_id: UserProfileId;
  address_id: AddressId | null;
};

// Conditional type to check if a field is required in the database
export type IsRequired<T, K extends keyof T> = T[K] extends null | undefined
  ? false
  : true;

// Extract all required fields from a database table type
export type ExtractRequired<T> = {
  [K in keyof T as T[K] extends null | undefined ? never : K]: T[K];
};

// Extract all optional fields from a database table type
export type ExtractOptional<T> = {
  [K in keyof T as T[K] extends null | undefined ? K : never]: T[K];
};
