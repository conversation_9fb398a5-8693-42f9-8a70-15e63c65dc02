/**
 * Examples demonstrating the advanced TypeScript patterns used in user-profiles.ts
 * 
 * This file shows how the database-derived types can be used in practice,
 * showcasing the power of TypeScript utility types and type safety.
 */

import {
  Address,
  UserProfile,
  Shop,
  CreateAddressData,
  UpdateUserProfileData,
  UserProfileWithBilling,
  ShopWithAddress,
  AddressFields,
  StringFields,
  OptionalFields,
  ExtractRequired,
  ExtractOptional,
  TypeSafeAddress,
  AddressId,
  createAddressId,
  isUserType,
  isValidAddress,
  DeepPartial,
  UserType,
} from './user-profiles'

// Example 1: Using Pick to create a type with only specific fields
type AddressDisplayInfo = Pick<Address, 'nickname' | 'city' | 'country'>

const displayAddress: AddressDisplayInfo = {
  nickname: 'Home',
  city: 'New York',
  country: 'USA'
}

// Example 2: Using Omit to exclude fields we don't want
type AddressWithoutTimestamps = Omit<Address, 'created_at' | 'updated_at'>

// Example 3: Using the CreateAddressData type (which already omits auto-generated fields)
const newAddressData: CreateAddressData = {
  nickname: 'Office',
  address_line_1: '123 Business St',
  city: 'San Francisco',
  country: 'USA'
  // Note: id, created_at, updated_at are automatically excluded
}

// Example 4: Using conditional types to extract field types
type AddressStringFields = StringFields<Address>
// Result: "id" | "nickname" | "address_line_1" | "address_line_2" | "city" | "state_province" | "postal_code" | "country" | "created_at" | "updated_at"

type AddressOptionalFields = OptionalFields<Address>
// Result: "address_line_2" | "state_province" | "postal_code"

// Example 5: Using ExtractRequired and ExtractOptional
type RequiredAddressFields = ExtractRequired<Address>
type OptionalAddressFields = ExtractOptional<Address>

// Example 6: Using branded types for type safety
const addressId = createAddressId('123e4567-e89b-12d3-a456-426614174000')
const typeSafeAddress: TypeSafeAddress = {
  id: addressId,
  nickname: 'Main Office',
  address_line_1: '456 Corporate Blvd',
  city: 'Austin',
  country: 'USA',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

// Example 7: Using type guards for runtime validation
function processUserType(input: string) {
  if (isUserType(input)) {
    // TypeScript now knows input is UserType ('creator' | 'store')
    console.log(`Valid user type: ${input}`)
    return input
  }
  throw new Error(`Invalid user type: ${input}`)
}

// Example 8: Using DeepPartial for nested optional updates
type DeepPartialProfile = DeepPartial<UserProfileWithBilling>

const partialUpdate: DeepPartialProfile = {
  store_name: 'Updated Store Name',
  // All other fields are optional, including nested ones
}

// Example 9: Creating a form validation function using our types
function validateAddressForm(data: unknown): data is CreateAddressData {
  if (typeof data !== 'object' || data === null) return false
  
  const address = data as Partial<CreateAddressData>
  
  return !!(
    address.nickname &&
    address.address_line_1 &&
    address.city &&
    address.country
  )
}

// Example 10: Using mapped types to create a validation schema
type ValidationSchema<T> = {
  [K in keyof T]: {
    required: boolean
    type: string
    validator?: (value: T[K]) => boolean
  }
}

const addressValidationSchema: ValidationSchema<CreateAddressData> = {
  nickname: {
    required: true,
    type: 'string',
    validator: (value) => typeof value === 'string' && value.length > 0
  },
  address_line_1: {
    required: true,
    type: 'string',
    validator: (value) => typeof value === 'string' && value.length > 0
  },
  address_line_2: {
    required: false,
    type: 'string'
  },
  city: {
    required: true,
    type: 'string',
    validator: (value) => typeof value === 'string' && value.length > 0
  },
  state_province: {
    required: false,
    type: 'string'
  },
  postal_code: {
    required: false,
    type: 'string'
  },
  country: {
    required: true,
    type: 'string',
    validator: (value) => typeof value === 'string' && value.length > 0
  }
}

// Example 11: Using utility types for API responses
type ApiResult<T> = {
  success: true
  data: T
} | {
  success: false
  error: string
}

// This leverages our existing types
function createAddress(data: CreateAddressData): ApiResult<Address> {
  if (!validateAddressForm(data)) {
    return { success: false, error: 'Invalid address data' }
  }
  
  // Simulate creating an address
  const address: Address = {
    id: crypto.randomUUID(),
    ...data,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  return { success: true, data: address }
}

// Example 12: Using template literal types for dynamic field access
type AddressFieldKey = `address_${string}`
type UserProfileFieldKey = `profile_${string}`

// Example 13: Creating a type-safe field selector
function selectFields<T, K extends keyof T>(obj: T, fields: K[]): Pick<T, K> {
  const result = {} as Pick<T, K>
  fields.forEach(field => {
    result[field] = obj[field]
  })
  return result
}

// Usage with type safety
const address: Address = {
  id: '123',
  nickname: 'Home',
  address_line_1: '123 Main St',
  city: 'Anytown',
  country: 'USA',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const selectedFields = selectFields(address, ['nickname', 'city', 'country'])
// Type is Pick<Address, 'nickname' | 'city' | 'country'>

// Example 14: Using conditional types for complex logic
type IsStringField<T, K extends keyof T> = T[K] extends string ? true : false

type AddressNicknameIsString = IsStringField<Address, 'nickname'> // true
type AddressIdIsString = IsStringField<Address, 'id'> // true

export {
  displayAddress,
  newAddressData,
  typeSafeAddress,
  processUserType,
  validateAddressForm,
  addressValidationSchema,
  createAddress,
  selectFields,
  selectedFields
}
