import { createClient } from "@/lib/supabase/client";
import { Database } from "@/lib/types/database";

type Order = Database["public"]["Tables"]["orders"]["Row"];
type OrderItem = Database["public"]["Tables"]["order_items"]["Row"];
type StoreCreatorRelationship =
  Database["public"]["Tables"]["store_creator_relationships"]["Row"];

export interface OrderWithDetails extends Order {
  store_name: string;
  creator_name: string;
  items: (OrderItem & {
    product_name: string;
    sku: string | null;
    color: string | null;
    scent: string | null;
  })[];
}

export interface CreatorWithRelationship {
  id: string;
  store_name: string;
  contact_email: string | null;
  contact_phone: string | null;
  relationship_created_at: string;
  is_active: boolean;
}

export interface StoreRequest {
  id: string;
  store_id: string;
  store_name: string;
  contact_email: string | null;
  contact_phone: string | null;
  created_at: string;
  status: "pending" | "approved" | "rejected";
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Get orders for current user (both as store and creator)
export async function getCurrentUserOrders(): Promise<
  ApiResponse<OrderWithDetails[]>
> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from("orders")
      .select(`
        *,
        store:user_profiles!orders_store_id_fkey(store_name),
        creator:user_profiles!orders_creator_id_fkey(store_name),
        order_items(
          *,
          product_offering:user_product_offerings(
            product:products(
              product_type:product_types(name),
              sku,
              color,
              scent
            )
          )
        )
      `)
      .order("created_at", { ascending: false });

    if (error) {
      return { success: false, error: error.message };
    }

    const ordersWithDetails: OrderWithDetails[] = data.map((order) => ({
      ...order,
      store_name: order.store?.store_name || "Unknown Store",
      creator_name: order.creator?.store_name || "Unknown Creator",
      items: order.order_items.map((item) => ({
        ...item,
        product_name: item.product_offering?.product?.product_type?.name ||
          "Unknown Product",
        sku: item.product_offering?.product?.sku || null,
        color: item.product_offering?.product?.color || null,
        scent: item.product_offering?.product?.scent || null,
      })),
    }));

    return { success: true, data: ordersWithDetails };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch orders",
    };
  }
}

// Get store-creator relationships for current user
export async function getCurrentUserRelationships(): Promise<
  ApiResponse<CreatorWithRelationship[]>
> {
  try {
    const supabase = createClient();

    // Get current user profile
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    const { data: profile } = await supabase
      .from("user_profiles")
      .select("id, user_type")
      .eq("user_id", user.id)
      .single();

    if (!profile) {
      return { success: false, error: "User profile not found" };
    }

    let query;
    if (profile.user_type === "store") {
      // Get creators this store works with (only approved relationships)
      query = supabase
        .from("store_creator_relationships")
        .select(`
          created_at,
          is_active,
          creator:user_profiles!store_creator_relationships_creator_id_fkey(
            id,
            store_name,
            contact_email,
            contact_phone
          )
        `)
        .eq("store_id", profile.id)
        .eq("status", "approved")
        .eq("is_active", true);
    } else {
      // Get stores this creator works with (only approved relationships)
      query = supabase
        .from("store_creator_relationships")
        .select(`
          created_at,
          is_active,
          store:user_profiles!store_creator_relationships_store_id_fkey(
            id,
            store_name,
            contact_email,
            contact_phone
          )
        `)
        .eq("creator_id", profile.id)
        .eq("status", "approved")
        .eq("is_active", true);
    }

    const { data, error } = await query;

    if (error) {
      return { success: false, error: error.message };
    }

    const relationships: CreatorWithRelationship[] = data.map((rel) => {
      const partner = profile.user_type === "store" ? rel.creator : rel.store;
      return {
        id: partner.id,
        store_name: partner.store_name,
        contact_email: partner.contact_email,
        contact_phone: partner.contact_phone,
        relationship_created_at: rel.created_at,
        is_active: rel.is_active,
      };
    });

    return { success: true, data: relationships };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error
        ? error.message
        : "Failed to fetch relationships",
    };
  }
}

// Create a new store-creator relationship
export async function createStoreCreatorRelationship(
  creatorName: string,
): Promise<ApiResponse<StoreCreatorRelationship>> {
  try {
    const supabase = createClient();

    // Get current user profile (must be a store)
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    const { data: storeProfile } = await supabase
      .from("user_profiles")
      .select("id, user_type")
      .eq("user_id", user.id)
      .single();

    if (!storeProfile || storeProfile.user_type !== "store") {
      return {
        success: false,
        error: "Only stores can create relationships with creators",
      };
    }

    // Find creator by name
    const { data: creatorProfile } = await supabase
      .from("user_profiles")
      .select("id")
      .eq("store_name", creatorName)
      .eq("user_type", "creator")
      .single();

    if (!creatorProfile) {
      return {
        success: false,
        error:
          `No creator found with the name "${creatorName}". Please reach out to the creator directly to set up a store account or request clarification.`,
      };
    }

    // Check if relationship already exists
    const { data: existingRelationship } = await supabase
      .from("store_creator_relationships")
      .select("id, is_active, status")
      .eq("store_id", storeProfile.id)
      .eq("creator_id", creatorProfile.id)
      .single();

    if (existingRelationship) {
      if (existingRelationship.status === "pending") {
        return {
          success: false,
          error:
            "You already have a pending request with this creator. Please wait for them to respond.",
        };
      } else if (
        existingRelationship.status === "approved" &&
        existingRelationship.is_active
      ) {
        return {
          success: false,
          error: "You already have an active relationship with this creator",
        };
      } else if (existingRelationship.status === "rejected") {
        return {
          success: false,
          error:
            "Your previous request to work with this creator was rejected. Please contact them directly.",
        };
      } else if (
        existingRelationship.status === "approved" &&
        !existingRelationship.is_active
      ) {
        // Reactivate existing approved relationship
        const { data, error } = await supabase
          .from("store_creator_relationships")
          .update({ is_active: true })
          .eq("id", existingRelationship.id)
          .select()
          .single();

        if (error) {
          return { success: false, error: error.message };
        }

        return { success: true, data };
      }
    }

    // Create new pending request
    const { data, error } = await supabase
      .from("store_creator_relationships")
      .insert({
        store_id: storeProfile.id,
        creator_id: creatorProfile.id,
        status: "pending",
        is_active: false,
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error
        ? error.message
        : "Failed to create relationship",
    };
  }
}

// Update order status
export async function updateOrderStatus(
  orderId: string,
  status: Order["status"],
): Promise<ApiResponse<Order>> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from("orders")
      .update({ status })
      .eq("id", orderId)
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error
        ? error.message
        : "Failed to update order status",
    };
  }
}

// Get creator's product offerings for order placement
export async function getCreatorProductOfferings(
  creatorId: string,
): Promise<ApiResponse<unknown[]>> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from("user_product_offerings")
      .select(`
        *,
        product:products(
          *,
          product_type:product_types(*)
        )
      `)
      .eq("creator_id", creatorId)
      .eq("is_available", true)
      .gt("stock_quantity", 0)
      .order("display_order");

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error
        ? error.message
        : "Failed to fetch creator products",
    };
  }
}

// Get pending requests for current creator
export async function getCreatorPendingRequests(): Promise<
  ApiResponse<StoreRequest[]>
> {
  try {
    const supabase = createClient();

    // Get current user profile (must be a creator)
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    const { data: creatorProfile } = await supabase
      .from("user_profiles")
      .select("id, user_type")
      .eq("user_id", user.id)
      .single();

    if (!creatorProfile || creatorProfile.user_type !== "creator") {
      return {
        success: false,
        error: "Only creators can view pending requests",
      };
    }

    // Get pending requests for this creator
    const { data, error } = await supabase
      .from("store_creator_relationships")
      .select(`
        id,
        store_id,
        created_at,
        status,
        store:user_profiles!store_creator_relationships_store_id_fkey(
          store_name,
          contact_email,
          contact_phone
        )
      `)
      .eq("creator_id", creatorProfile.id)
      .eq("status", "pending")
      .order("created_at", { ascending: false });

    if (error) {
      return { success: false, error: error.message };
    }

    const requests: StoreRequest[] = data.map((req) => ({
      id: req.id,
      store_id: req.store_id,
      store_name: req.store.store_name,
      contact_email: req.store.contact_email,
      contact_phone: req.store.contact_phone,
      created_at: req.created_at,
      status: req.status as "pending" | "approved" | "rejected",
    }));

    return { success: true, data: requests };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error
        ? error.message
        : "Failed to fetch pending requests",
    };
  }
}

// Approve or reject a store request
export async function updateRequestStatus(
  requestId: string,
  status: "approved" | "rejected",
): Promise<ApiResponse<StoreCreatorRelationship>> {
  try {
    const supabase = createClient();

    // Get current user profile (must be a creator)
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: "User not authenticated" };
    }

    const { data: creatorProfile } = await supabase
      .from("user_profiles")
      .select("id, user_type")
      .eq("user_id", user.id)
      .single();

    if (!creatorProfile || creatorProfile.user_type !== "creator") {
      return {
        success: false,
        error: "Only creators can update request status",
      };
    }

    // Update the request status
    const { data, error } = await supabase
      .from("store_creator_relationships")
      .update({
        status,
        is_active: status === "approved", // Only activate if approved
      })
      .eq("id", requestId)
      .eq("creator_id", creatorProfile.id) // Ensure creator owns this request
      .eq("status", "pending") // Only update pending requests
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    if (!data) {
      return {
        success: false,
        error: "Request not found or already processed",
      };
    }

    return { success: true, data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error
        ? error.message
        : "Failed to update request status",
    };
  }
}
