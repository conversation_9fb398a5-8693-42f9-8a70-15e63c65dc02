import { createClient } from "@/lib/supabase/client";
import {
  CreateOfferingData,
  GetCurrentUserProductOfferingsResult,
  Product,
  ProductType,
  UpdateOfferingData,
  UserProductOffering,
} from "@/lib/types/product-offerings";
import {
  CurrentUserProductOfferings,
  CurrentUserProductOfferingsResponse,
} from "@/lib/types/views";

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export type ProductOfferingResponse = ApiResponse<UserProductOffering>;
export type ProductOfferingsResponse = ApiResponse<
  CurrentUserProductOfferings[]
>;
export type ProductsResponse = ApiResponse<
  (Product & { product_type: ProductType })[]
>;

// Get current user's product offerings
export async function getCurrentUserProductOfferings(): Promise<
  ProductOfferingsResponse
> {
  try {
    const supabase = createClient();

    // Use the new RLS-secured view instead of RPC function
    // This provides full type safety and is easier to debug
    const { data, error } = await supabase
      .from("current_user_product_offerings")
      .select("*")
      .order("is_featured", { ascending: false })
      .order("display_order", { ascending: true })
      .order("product_name", { ascending: true });

    if (error) {
      return { error: error.message };
    }

    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch product offerings",
    };
  }
}

// Get all available products for creating new offerings
export async function getAvailableProducts(): Promise<ProductsResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from("products")
      .select(`
        *,
        product_type:product_types(*)
      `)
      .eq("active", true)
      .order("product_type(name)", { ascending: true });

    if (error) {
      return { error: error.message };
    }

    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch products",
    };
  }
}

// Create a new product offering
export async function createProductOffering(
  offeringData: CreateOfferingData,
): Promise<ProductOfferingResponse> {
  try {
    const supabase = createClient();

    // Get current user profile to get user_profile_id
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { error: "User not authenticated" };
    }

    const { data: profile } = await supabase
      .from("user_profiles")
      .select("id")
      .eq("user_id", user.id)
      .eq("user_type", "creator")
      .single();

    if (!profile) {
      return { error: "Creator profile not found" };
    }

    const { data, error } = await supabase
      .from("user_product_offerings")
      .insert({
        ...offeringData,
        user_profile_id: profile.id,
      })
      .select()
      .single();

    if (error) {
      return { error: error.message };
    }

    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to create product offering",
    };
  }
}

// Update an existing product offering
export async function updateProductOffering(
  offeringId: string,
  offeringData: UpdateOfferingData,
): Promise<ProductOfferingResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from("user_product_offerings")
      .update(offeringData)
      .eq("id", offeringId)
      .select()
      .single();

    if (error) {
      return { error: error.message };
    }

    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to update product offering",
    };
  }
}

// Update stock quantity using direct query with RLS
// RLS policy ensures users can only update their own offerings
export async function updateOfferingStock(
  offeringId: string,
  newQuantity: number,
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = createClient();

    // Direct UPDATE query with RLS providing security
    // The existing RLS policy ensures only creators can update their own offerings
    const { data, error } = await supabase
      .from("user_product_offerings")
      .update({
        stock_quantity: newQuantity,
        updated_at: new Date().toISOString(),
      })
      .eq("id", offeringId)
      .select("id")
      .single();

    if (error) {
      return { error: error.message };
    }

    // If no rows were updated, the user doesn't own this offering
    // This happens when RLS policy blocks the update
    if (!data) {
      return {
        error: "Failed to update stock - you may not own this offering",
      };
    }

    return { data: true };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to update stock",
    };
  }
}

// Toggle availability using direct query with RLS
// RLS policy ensures users can only update their own offerings
export async function toggleOfferingAvailability(
  offeringId: string,
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = createClient();

    // First, get the current availability status
    const { data: currentOffering, error: fetchError } = await supabase
      .from("user_product_offerings")
      .select("is_available")
      .eq("id", offeringId)
      .single();

    if (fetchError) {
      return { error: fetchError.message };
    }

    if (!currentOffering) {
      return {
        error: "Failed to toggle availability - you may not own this offering",
      };
    }

    // Toggle the availability and update
    const { data, error } = await supabase
      .from("user_product_offerings")
      .update({
        is_available: !currentOffering.is_available,
        updated_at: new Date().toISOString(),
      })
      .eq("id", offeringId)
      .select("id")
      .single();

    if (error) {
      return { error: error.message };
    }

    // If no rows were updated, the user doesn't own this offering
    // This happens when RLS policy blocks the update
    if (!data) {
      return {
        error: "Failed to toggle availability - you may not own this offering",
      };
    }

    return { data: true };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to toggle availability",
    };
  }
}

// Delete a product offering
export async function deleteProductOffering(
  offeringId: string,
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = createClient();

    const { error } = await supabase
      .from("user_product_offerings")
      .delete()
      .eq("id", offeringId);

    if (error) {
      return { error: error.message };
    }

    return { data: true };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to delete product offering",
    };
  }
}
