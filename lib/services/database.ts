import { createClient } from "@/lib/supabase/server";
import {
  ApiResponse,
  CommissionRate,
  CreateCustomerData,
  CreateProductData,
  CreateProductTypeData,
  Customer,
  Invoice,
  InvoiceFormData,
  InvoiceWithRelations,
  PaginatedResponse,
  Product,
  ProductType,
  ProductWithType,
} from "@/lib/types/database";

// Customer operations
export async function getCustomers(): Promise<ApiResponse<Customer[]>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("customers")
      .select("*")
      .order("relationship_started_date", { ascending: false });

    if (error) throw error;
    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch customers",
    };
  }
}

export async function getCustomerById(
  id: string,
): Promise<ApiResponse<Customer>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("customers")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch customer",
    };
  }
}

export async function createCustomer(
  customerData: CreateCustomerData,
): Promise<ApiResponse<Customer>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("customers")
      .insert(customerData)
      .select()
      .single();

    if (error) throw error;
    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to create customer",
    };
  }
}

// Product Type operations
export async function getProductTypes(): Promise<ApiResponse<ProductType[]>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("product_types")
      .select("*")
      .order("name");

    if (error) throw error;
    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch product types",
    };
  }
}

export async function createProductType(
  productTypeData: CreateProductTypeData,
): Promise<ApiResponse<ProductType>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("product_types")
      .insert(productTypeData)
      .select()
      .single();

    if (error) throw error;
    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to create product type",
    };
  }
}

// Product operations
export async function getProducts(): Promise<ApiResponse<ProductWithType[]>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("products")
      .select(`
        *,
        product_type:product_types(*)
      `)
      .eq("active", true)
      .order("sku");

    if (error) throw error;
    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch products",
    };
  }
}

export async function createProduct(
  productData: CreateProductData,
): Promise<ApiResponse<Product>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("products")
      .insert(productData)
      .select(`
        *,
        product_type:product_types(*)
      `)
      .single();

    if (error) throw error;
    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to create product",
    };
  }
}

// Commission Rate operations
export async function getCommissionRates(): Promise<
  ApiResponse<CommissionRate[]>
> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("commission_rates")
      .select("*")
      .eq("active", true)
      .order("sales_channel");

    if (error) throw error;
    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch commission rates",
    };
  }
}

// Invoice operations
export async function getInvoices(
  page = 1,
  limit = 20,
): Promise<ApiResponse<PaginatedResponse<InvoiceWithRelations>>> {
  try {
    const supabase = await createClient();
    const offset = (page - 1) * limit;

    const { data, error, count } = await supabase
      .from("invoices")
      .select(
        `
        *,
        customer:customers(*),
        invoice_items(
          *,
          product:products(
            *,
            product_type:product_types(*)
          )
        )
      `,
        { count: "exact" },
      )
      .order("invoice_date", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    const totalPages = Math.ceil((count || 0) / limit);

    return {
      data: {
        data: data || [],
        count: count || 0,
        page,
        limit,
        pageSize: limit,
        totalPages,
      },
    };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch invoices",
    };
  }
}

export async function getInvoiceById(
  id: string,
): Promise<ApiResponse<Invoice>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("invoices")
      .select(`
        *,
        customer:customers(*),
        invoice_items(
          *,
          product:products(
            *,
            product_type:product_types(*)
          )
        )
      `)
      .eq("id", id)
      .single();

    if (error) throw error;
    return { data };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to fetch invoice",
    };
  }
}

// Get next invoice number
export async function getNextInvoiceNumber(): Promise<ApiResponse<number>> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("invoices")
      .select("invoice_number")
      .order("invoice_number", { ascending: false })
      .limit(1);

    if (error) throw error;

    const nextNumber = data && data.length > 0 ? data[0].invoice_number + 1 : 1;
    return { data: nextNumber };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to get next invoice number",
    };
  }
}

// Create invoice with items
export async function createInvoice(
  invoiceData: InvoiceFormData,
): Promise<ApiResponse<Invoice>> {
  try {
    const supabase = await createClient();

    // Get next invoice number
    const nextNumberResult = await getNextInvoiceNumber();
    if (nextNumberResult.error || !nextNumberResult.data) {
      throw new Error(
        nextNumberResult.error || "Failed to get next invoice number",
      );
    }

    // Calculate totals
    const subtotal = invoiceData.items.reduce(
      (sum, item) => sum + (item.unit_price * item.quantity),
      0,
    );
    const vatPercentage = invoiceData.vat_percentage || 21;
    const vatAmount = (subtotal * vatPercentage) / 100;
    const shippingAmount = invoiceData.shipping_amount || 0;
    const totalAmount = subtotal + vatAmount + shippingAmount;

    // Create invoice
    const { data: invoice, error: invoiceError } = await supabase
      .from("invoices")
      .insert({
        invoice_number: nextNumberResult.data,
        customer_id: invoiceData.customer_id,
        invoice_date: invoiceData.invoice_date,
        due_date: invoiceData.due_date,
        sales_channel: invoiceData.sales_channel,
        payment_method: invoiceData.payment_method,
        subtotal,
        commission_amount: 0, // Will be calculated based on commission rates
        shipping_amount: shippingAmount,
        vat_percentage: vatPercentage,
        vat_amount: vatAmount,
        total_amount: totalAmount,
        notes: invoiceData.notes,
        status: "draft",
      })
      .select()
      .single();

    if (invoiceError) throw invoiceError;

    // Create invoice items
    const itemsWithInvoiceId = invoiceData.items.map((item) => ({
      invoice_id: invoice.id,
      product_id: item.product_id,
      description: item.description,
      unit_price: item.unit_price,
      quantity: item.quantity,
      line_total: item.unit_price * item.quantity,
    }));

    const { error: itemsError } = await supabase
      .from("invoice_items")
      .insert(itemsWithInvoiceId);

    if (itemsError) throw itemsError;

    // Return the complete invoice with items
    return await getInvoiceById(invoice.id);
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to create invoice",
    };
  }
}
