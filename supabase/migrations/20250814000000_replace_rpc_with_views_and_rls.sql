-- Migration: Replace RPC functions with RLS-secured views for better type safety
-- This migration creates secure views that replace RPC functions while maintaining security

-- ============================================================================
-- 1. Create RLS-secured view for current user profile with billing address
-- ============================================================================

-- This view replaces get_current_user_profile() RPC function
-- It automatically filters to show only the current user's profile
CREATE OR REPLACE VIEW current_user_profile_with_billing AS
SELECT 
    up.id,
    up.user_id,
    up.store_name,
    up.store_logo_url,
    up.contact_email,
    up.contact_phone,
    up.user_type,
    up.is_active,
    up.created_at,
    up.updated_at,
    up.billing_address_id,
    up.store_name_uri,
    -- Billing address fields (flattened for easier access)
    ba.nickname as billing_address_nickname,
    ba.address_line_1 as billing_address_line_1,
    ba.address_line_2 as billing_address_line_2,
    ba.city as billing_city,
    ba.state_province as billing_state_province,
    ba.postal_code as billing_postal_code,
    ba.country as billing_country
FROM user_profiles up
LEFT JOIN addresses ba ON up.billing_address_id = ba.id
WHERE up.user_id = auth.uid();

-- Enable RLS on the view (inherits from underlying tables)
ALTER VIEW current_user_profile_with_billing OWNER TO postgres;

-- Grant permissions
GRANT SELECT ON current_user_profile_with_billing TO authenticated;
GRANT SELECT ON current_user_profile_with_billing TO anon;

-- ============================================================================
-- 2. Create RLS-secured view for current user shops with addresses
-- ============================================================================

-- This view replaces get_current_user_shops() RPC function
CREATE OR REPLACE VIEW current_user_shops_with_addresses AS
SELECT 
    s.id,
    s.store_id,
    s.shop_nickname,
    s.is_active,
    s.notes,
    s.created_at,
    s.updated_at,
    s.address_id,
    -- Address fields (flattened for easier access)
    a.nickname as address_nickname,
    a.address_line_1,
    a.address_line_2,
    a.city,
    a.state_province,
    a.postal_code,
    a.country
FROM shops s
LEFT JOIN addresses a ON s.address_id = a.id
JOIN user_profiles up ON s.store_id = up.id
WHERE up.user_id = auth.uid();

-- Enable RLS on the view (inherits from underlying tables)
ALTER VIEW current_user_shops_with_addresses OWNER TO postgres;

-- Grant permissions
GRANT SELECT ON current_user_shops_with_addresses TO authenticated;
GRANT SELECT ON current_user_shops_with_addresses TO anon;

-- ============================================================================
-- 3. Create RLS-secured view for current user product offerings
-- ============================================================================

-- This view replaces get_current_user_product_offerings() RPC function
-- It uses the existing creator_product_offerings view but filters for current user
CREATE OR REPLACE VIEW current_user_product_offerings AS
SELECT 
    cpo.offering_id,
    cpo.product_id,
    cpo.product_name,
    cpo.product_description,
    cpo.sku,
    cpo.color,
    cpo.scent,
    cpo.base_price,
    cpo.custom_price,
    cpo.effective_price,
    cpo.stock_quantity,
    cpo.min_stock_level,
    cpo.is_available,
    cpo.is_featured,
    cpo.creator_notes,
    cpo.display_order,
    cpo.stock_status,
    cpo.availability_status,
    cpo.weight_grams,
    cpo.offering_created_at,
    cpo.offering_updated_at
FROM creator_product_offerings cpo
WHERE cpo.creator_user_id = auth.uid();

-- Enable RLS on the view (inherits from underlying tables)
ALTER VIEW current_user_product_offerings OWNER TO postgres;

-- Grant permissions
GRANT SELECT ON current_user_product_offerings TO authenticated;
GRANT SELECT ON current_user_product_offerings TO anon;

-- ============================================================================
-- 4. Add proper TypeScript return types for remaining RPC functions
-- ============================================================================

-- We'll keep update_offering_stock and toggle_offering_availability as RPC functions
-- because they contain important authorization logic and atomic operations
-- But we'll add comments to help with TypeScript typing

COMMENT ON FUNCTION update_offering_stock(uuid, integer) IS 
'Updates stock quantity for a user product offering. Returns boolean indicating success.
TypeScript type: (offeringId: string, newQuantity: number) => Promise<boolean>';

COMMENT ON FUNCTION toggle_offering_availability(uuid) IS 
'Toggles availability status for a user product offering. Returns boolean indicating success.
TypeScript type: (offeringId: string) => Promise<boolean>';

-- ============================================================================
-- 5. Create indexes for better performance on the new views
-- ============================================================================

-- Index for current user profile lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id_active 
ON user_profiles(user_id) WHERE is_active = true;

-- Index for current user shops lookups  
CREATE INDEX IF NOT EXISTS idx_shops_store_id_active 
ON shops(store_id) WHERE is_active = true;

-- Indexes for product offerings are already created in previous migration
