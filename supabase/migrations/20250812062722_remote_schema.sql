

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."customer_type" AS ENUM (
    'individual',
    'company'
);


ALTER TYPE "public"."customer_type" OWNER TO "postgres";


CREATE TYPE "public"."invoice_status" AS ENUM (
    'draft',
    'sent',
    'paid',
    'overdue',
    'cancelled'
);


ALTER TYPE "public"."invoice_status" OWNER TO "postgres";


CREATE TYPE "public"."payment_method" AS ENUM (
    'cash',
    'card',
    'bank_transfer',
    'shopify'
);


ALTER TYPE "public"."payment_method" OWNER TO "postgres";


CREATE TYPE "public"."sales_channel" AS ENUM (
    'in_store',
    'website',
    'partner_store'
);


ALTER TYPE "public"."sales_channel" OWNER TO "postgres";


CREATE TYPE "public"."user_type_enum" AS ENUM (
    'creator',
    'store'
);


ALTER TYPE "public"."user_type_enum" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_current_user_profile"() RETURNS TABLE("id" "uuid", "user_id" "uuid", "store_name" character varying, "store_logo_url" "text", "contact_email" character varying, "contact_phone" character varying, "user_type" "public"."user_type_enum", "is_active" boolean, "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "billing_address_id" "uuid", "billing_address_nickname" character varying, "billing_address_line_1" character varying, "billing_address_line_2" character varying, "billing_city" character varying, "billing_state_province" character varying, "billing_postal_code" character varying, "billing_country" character varying)
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    SELECT * FROM user_profiles_with_billing
    WHERE user_id = auth.uid();
$$;


ALTER FUNCTION "public"."get_current_user_profile"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_current_user_shops"() RETURNS TABLE("id" "uuid", "store_id" "uuid", "shop_nickname" character varying, "is_active" boolean, "notes" "text", "created_at" timestamp with time zone, "updated_at" timestamp with time zone, "address_id" "uuid", "address_nickname" character varying, "address_line_1" character varying, "address_line_2" character varying, "city" character varying, "state_province" character varying, "postal_code" character varying, "country" character varying)
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
    SELECT swa.* FROM shops_with_addresses swa
    JOIN user_profiles up ON swa.store_id = up.id
    WHERE up.user_id = auth.uid();
$$;


ALTER FUNCTION "public"."get_current_user_shops"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Create a basic user profile for new users
    -- They will need to complete their profile later
    INSERT INTO user_profiles (user_id, store_name, user_type)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'store_name', 'New Store'),
        COALESCE((NEW.raw_user_meta_data->>'user_type')::user_type_enum, 'store')
    );
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."addresses" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "nickname" character varying(100) NOT NULL,
    "address_line_1" character varying(255) NOT NULL,
    "address_line_2" character varying(255),
    "city" character varying(100) NOT NULL,
    "state_province" character varying(100),
    "postal_code" character varying(20),
    "country" character varying(100) DEFAULT 'United States'::character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."addresses" OWNER TO "postgres";


COMMENT ON TABLE "public"."addresses" IS 'Sample addresses created for demonstration. Real data will be created when users sign up.';



CREATE TABLE IF NOT EXISTS "public"."commission_rates" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "sales_channel" "public"."sales_channel" NOT NULL,
    "payment_method" "public"."payment_method",
    "rate_percentage" numeric(5,2) NOT NULL,
    "description" character varying(255),
    "active" boolean DEFAULT true
);


ALTER TABLE "public"."commission_rates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."customers" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "type" "public"."customer_type" NOT NULL,
    "company_name" character varying(255),
    "nif" character varying(20),
    "vat_number" character varying(30),
    "first_name" character varying(100),
    "last_name" character varying(100),
    "email" character varying(255),
    "phone" character varying(20),
    "street_address" character varying(255),
    "city" character varying(100),
    "zip_code" character varying(10),
    "country" character varying(100) DEFAULT 'Spain'::character varying,
    "commission_percentage" numeric(5,2),
    "relationship_started_date" "date" DEFAULT CURRENT_DATE,
    CONSTRAINT "check_company_required_fields" CHECK ((("type" <> 'company'::"public"."customer_type") OR (("company_name" IS NOT NULL) AND ("nif" IS NOT NULL)))),
    CONSTRAINT "check_individual_required_fields" CHECK ((("type" <> 'individual'::"public"."customer_type") OR (("first_name" IS NOT NULL) AND ("last_name" IS NOT NULL))))
);


ALTER TABLE "public"."customers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."invoice_items" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "invoice_id" "uuid" NOT NULL,
    "product_id" "uuid" NOT NULL,
    "details" "text",
    "unit_price" numeric(10,2) NOT NULL,
    "quantity" integer NOT NULL,
    "line_total" numeric(10,2) NOT NULL
);


ALTER TABLE "public"."invoice_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."invoices" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "invoice_number" integer NOT NULL,
    "customer_id" "uuid" NOT NULL,
    "invoice_date" "date" NOT NULL,
    "due_date" "date",
    "status" "public"."invoice_status" DEFAULT 'draft'::"public"."invoice_status",
    "sales_channel" "public"."sales_channel" NOT NULL,
    "payment_method" "public"."payment_method",
    "subtotal" numeric(10,2) NOT NULL,
    "commission_amount" numeric(10,2) DEFAULT 0,
    "commission_percentage" numeric(5,2),
    "shipping_amount" numeric(10,2) DEFAULT 0,
    "vat_percentage" numeric(5,2) DEFAULT 21.00,
    "vat_amount" numeric(10,2) NOT NULL,
    "total_amount" numeric(10,2) NOT NULL,
    "payment_reference" "text",
    "paid_date" "date",
    "bank_name" character varying(100),
    "bank_account_iban" character varying(34),
    "bank_swift_bic" character varying(11),
    "notes" "text"
);


ALTER TABLE "public"."invoices" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."product_types" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "weight_grams" integer,
    "price" numeric(10,2) NOT NULL
);


ALTER TABLE "public"."product_types" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "product_type_id" "uuid" NOT NULL,
    "sku" "text",
    "color" "text",
    "scent" "text",
    "active" boolean DEFAULT true
);


ALTER TABLE "public"."products" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."shops" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "store_id" "uuid" NOT NULL,
    "shop_nickname" character varying(100) NOT NULL,
    "address_id" "uuid",
    "is_active" boolean DEFAULT true NOT NULL,
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."shops" OWNER TO "postgres";


COMMENT ON TABLE "public"."shops" IS 'Shops will be created by store owners to represent their multiple locations.';



CREATE OR REPLACE VIEW "public"."shops_with_addresses" AS
 SELECT "s"."id",
    "s"."store_id",
    "s"."shop_nickname",
    "s"."is_active",
    "s"."notes",
    "s"."created_at",
    "s"."updated_at",
    "a"."id" AS "address_id",
    "a"."nickname" AS "address_nickname",
    "a"."address_line_1",
    "a"."address_line_2",
    "a"."city",
    "a"."state_province",
    "a"."postal_code",
    "a"."country"
   FROM ("public"."shops" "s"
     LEFT JOIN "public"."addresses" "a" ON (("s"."address_id" = "a"."id")));


ALTER VIEW "public"."shops_with_addresses" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_profiles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "store_name" character varying(255) NOT NULL,
    "store_logo_url" "text",
    "billing_address_id" "uuid",
    "contact_email" character varying(255),
    "contact_phone" character varying(50),
    "user_type" "public"."user_type_enum" DEFAULT 'store'::"public"."user_type_enum" NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."user_profiles" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_profiles" IS 'User profiles will be created automatically when users sign up and complete their profile.';



CREATE OR REPLACE VIEW "public"."user_profiles_with_billing" AS
 SELECT "up"."id",
    "up"."user_id",
    "up"."store_name",
    "up"."store_logo_url",
    "up"."contact_email",
    "up"."contact_phone",
    "up"."user_type",
    "up"."is_active",
    "up"."created_at",
    "up"."updated_at",
    "ba"."id" AS "billing_address_id",
    "ba"."nickname" AS "billing_address_nickname",
    "ba"."address_line_1" AS "billing_address_line_1",
    "ba"."address_line_2" AS "billing_address_line_2",
    "ba"."city" AS "billing_city",
    "ba"."state_province" AS "billing_state_province",
    "ba"."postal_code" AS "billing_postal_code",
    "ba"."country" AS "billing_country"
   FROM ("public"."user_profiles" "up"
     LEFT JOIN "public"."addresses" "ba" ON (("up"."billing_address_id" = "ba"."id")));


ALTER VIEW "public"."user_profiles_with_billing" OWNER TO "postgres";


ALTER TABLE ONLY "public"."addresses"
    ADD CONSTRAINT "addresses_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."commission_rates"
    ADD CONSTRAINT "commission_rates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."commission_rates"
    ADD CONSTRAINT "commission_rates_sales_channel_payment_method_key" UNIQUE ("sales_channel", "payment_method");



ALTER TABLE ONLY "public"."customers"
    ADD CONSTRAINT "customers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invoice_items"
    ADD CONSTRAINT "invoice_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_invoice_number_key" UNIQUE ("invoice_number");



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."product_types"
    ADD CONSTRAINT "product_types_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."product_types"
    ADD CONSTRAINT "product_types_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_product_type_id_color_scent_key" UNIQUE ("product_type_id", "color", "scent");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_sku_key" UNIQUE ("sku");



ALTER TABLE ONLY "public"."shops"
    ADD CONSTRAINT "shops_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."shops"
    ADD CONSTRAINT "unique_shop_nickname_per_store" UNIQUE ("store_id", "shop_nickname");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_user_id_key" UNIQUE ("user_id");



CREATE INDEX "idx_addresses_city" ON "public"."addresses" USING "btree" ("city");



CREATE INDEX "idx_addresses_country" ON "public"."addresses" USING "btree" ("country");



CREATE INDEX "idx_addresses_nickname" ON "public"."addresses" USING "btree" ("nickname");



CREATE INDEX "idx_customers_type" ON "public"."customers" USING "btree" ("type");



CREATE INDEX "idx_invoice_items_invoice_id" ON "public"."invoice_items" USING "btree" ("invoice_id");



CREATE INDEX "idx_invoice_items_product_id" ON "public"."invoice_items" USING "btree" ("product_id");



CREATE INDEX "idx_invoices_customer_id" ON "public"."invoices" USING "btree" ("customer_id");



CREATE INDEX "idx_invoices_invoice_date" ON "public"."invoices" USING "btree" ("invoice_date");



CREATE INDEX "idx_invoices_sales_channel" ON "public"."invoices" USING "btree" ("sales_channel");



CREATE INDEX "idx_invoices_status" ON "public"."invoices" USING "btree" ("status");



CREATE INDEX "idx_products_active" ON "public"."products" USING "btree" ("active");



CREATE INDEX "idx_products_color" ON "public"."products" USING "btree" ("color");



CREATE INDEX "idx_products_product_type_id" ON "public"."products" USING "btree" ("product_type_id");



CREATE INDEX "idx_products_scent" ON "public"."products" USING "btree" ("scent");



CREATE INDEX "idx_shops_address_id" ON "public"."shops" USING "btree" ("address_id");



CREATE INDEX "idx_shops_is_active" ON "public"."shops" USING "btree" ("is_active");



CREATE INDEX "idx_shops_nickname" ON "public"."shops" USING "btree" ("shop_nickname");



CREATE INDEX "idx_shops_store_id" ON "public"."shops" USING "btree" ("store_id");



CREATE INDEX "idx_user_profiles_is_active" ON "public"."user_profiles" USING "btree" ("is_active");



CREATE INDEX "idx_user_profiles_store_name" ON "public"."user_profiles" USING "btree" ("store_name");



CREATE INDEX "idx_user_profiles_user_id" ON "public"."user_profiles" USING "btree" ("user_id");



CREATE INDEX "idx_user_profiles_user_type" ON "public"."user_profiles" USING "btree" ("user_type");



CREATE OR REPLACE TRIGGER "update_addresses_updated_at" BEFORE UPDATE ON "public"."addresses" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_shops_updated_at" BEFORE UPDATE ON "public"."shops" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_user_profiles_updated_at" BEFORE UPDATE ON "public"."user_profiles" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."invoice_items"
    ADD CONSTRAINT "invoice_items_invoice_id_fkey" FOREIGN KEY ("invoice_id") REFERENCES "public"."invoices"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoice_items"
    ADD CONSTRAINT "invoice_items_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id");



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_product_type_id_fkey" FOREIGN KEY ("product_type_id") REFERENCES "public"."product_types"("id");



ALTER TABLE ONLY "public"."shops"
    ADD CONSTRAINT "shops_address_id_fkey" FOREIGN KEY ("address_id") REFERENCES "public"."addresses"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."shops"
    ADD CONSTRAINT "shops_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "public"."user_profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_billing_address_id_fkey" FOREIGN KEY ("billing_address_id") REFERENCES "public"."addresses"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Users can delete their addresses" ON "public"."addresses" FOR DELETE USING (("id" IN ( SELECT "user_profiles"."billing_address_id"
   FROM "public"."user_profiles"
  WHERE ("user_profiles"."user_id" = "auth"."uid"())
UNION
 SELECT "s"."address_id"
   FROM ("public"."shops" "s"
     JOIN "public"."user_profiles" "up" ON (("s"."store_id" = "up"."id")))
  WHERE ("up"."user_id" = "auth"."uid"()))));



CREATE POLICY "Users can delete their own profile" ON "public"."user_profiles" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can delete their shops" ON "public"."shops" FOR DELETE USING (("store_id" IN ( SELECT "user_profiles"."id"
   FROM "public"."user_profiles"
  WHERE ("user_profiles"."user_id" = "auth"."uid"()))));



CREATE POLICY "Users can insert addresses" ON "public"."addresses" FOR INSERT WITH CHECK (true);



CREATE POLICY "Users can insert their own profile" ON "public"."user_profiles" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert their shops" ON "public"."shops" FOR INSERT WITH CHECK (("store_id" IN ( SELECT "user_profiles"."id"
   FROM "public"."user_profiles"
  WHERE ("user_profiles"."user_id" = "auth"."uid"()))));



CREATE POLICY "Users can update their addresses" ON "public"."addresses" FOR UPDATE USING (("id" IN ( SELECT "user_profiles"."billing_address_id"
   FROM "public"."user_profiles"
  WHERE ("user_profiles"."user_id" = "auth"."uid"())
UNION
 SELECT "s"."address_id"
   FROM ("public"."shops" "s"
     JOIN "public"."user_profiles" "up" ON (("s"."store_id" = "up"."id")))
  WHERE ("up"."user_id" = "auth"."uid"()))));



CREATE POLICY "Users can update their own profile" ON "public"."user_profiles" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their shops" ON "public"."shops" FOR UPDATE USING (("store_id" IN ( SELECT "user_profiles"."id"
   FROM "public"."user_profiles"
  WHERE ("user_profiles"."user_id" = "auth"."uid"()))));



CREATE POLICY "Users can view their addresses" ON "public"."addresses" FOR SELECT USING (("id" IN ( SELECT "user_profiles"."billing_address_id"
   FROM "public"."user_profiles"
  WHERE ("user_profiles"."user_id" = "auth"."uid"())
UNION
 SELECT "s"."address_id"
   FROM ("public"."shops" "s"
     JOIN "public"."user_profiles" "up" ON (("s"."store_id" = "up"."id")))
  WHERE ("up"."user_id" = "auth"."uid"()))));



CREATE POLICY "Users can view their own profile" ON "public"."user_profiles" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their shops" ON "public"."shops" FOR SELECT USING (("store_id" IN ( SELECT "user_profiles"."id"
   FROM "public"."user_profiles"
  WHERE ("user_profiles"."user_id" = "auth"."uid"()))));



ALTER TABLE "public"."addresses" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."shops" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_profiles" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";

























































































































































GRANT ALL ON FUNCTION "public"."get_current_user_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_current_user_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_current_user_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_current_user_shops"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_current_user_shops"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_current_user_shops"() TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."addresses" TO "anon";
GRANT ALL ON TABLE "public"."addresses" TO "authenticated";
GRANT ALL ON TABLE "public"."addresses" TO "service_role";



GRANT ALL ON TABLE "public"."commission_rates" TO "anon";
GRANT ALL ON TABLE "public"."commission_rates" TO "authenticated";
GRANT ALL ON TABLE "public"."commission_rates" TO "service_role";



GRANT ALL ON TABLE "public"."customers" TO "anon";
GRANT ALL ON TABLE "public"."customers" TO "authenticated";
GRANT ALL ON TABLE "public"."customers" TO "service_role";



GRANT ALL ON TABLE "public"."invoice_items" TO "anon";
GRANT ALL ON TABLE "public"."invoice_items" TO "authenticated";
GRANT ALL ON TABLE "public"."invoice_items" TO "service_role";



GRANT ALL ON TABLE "public"."invoices" TO "anon";
GRANT ALL ON TABLE "public"."invoices" TO "authenticated";
GRANT ALL ON TABLE "public"."invoices" TO "service_role";



GRANT ALL ON TABLE "public"."product_types" TO "anon";
GRANT ALL ON TABLE "public"."product_types" TO "authenticated";
GRANT ALL ON TABLE "public"."product_types" TO "service_role";



GRANT ALL ON TABLE "public"."products" TO "anon";
GRANT ALL ON TABLE "public"."products" TO "authenticated";
GRANT ALL ON TABLE "public"."products" TO "service_role";



GRANT ALL ON TABLE "public"."shops" TO "anon";
GRANT ALL ON TABLE "public"."shops" TO "authenticated";
GRANT ALL ON TABLE "public"."shops" TO "service_role";



GRANT ALL ON TABLE "public"."shops_with_addresses" TO "anon";
GRANT ALL ON TABLE "public"."shops_with_addresses" TO "authenticated";
GRANT ALL ON TABLE "public"."shops_with_addresses" TO "service_role";



GRANT ALL ON TABLE "public"."user_profiles" TO "anon";
GRANT ALL ON TABLE "public"."user_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_profiles" TO "service_role";



GRANT ALL ON TABLE "public"."user_profiles_with_billing" TO "anon";
GRANT ALL ON TABLE "public"."user_profiles_with_billing" TO "authenticated";
GRANT ALL ON TABLE "public"."user_profiles_with_billing" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "service_role";






























RESET ALL;
