-- Add status field to store_creator_relationships table to support request workflow

-- Create enum for relationship status
CREATE TYPE relationship_status AS ENUM ('pending', 'approved', 'rejected');

-- Add status column to store_creator_relationships table
ALTER TABLE store_creator_relationships 
ADD COLUMN status relationship_status DEFAULT 'pending';

-- Update existing relationships to be 'approved' since they were created under the old system
UPDATE store_creator_relationships 
SET status = 'approved' 
WHERE status = 'pending';

-- Update RLS policies to handle the new status field

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own store-creator relationships" ON store_creator_relationships;
DROP POLICY IF EXISTS "Stores can create relationships with creators" ON store_creator_relationships;
DROP POLICY IF EXISTS "Users can update their own relationships" ON store_creator_relationships;

-- Recreate policies with status awareness

-- Users can view their own relationships (all statuses)
CREATE POLICY "Users can view their own store-creator relationships" ON store_creator_relationships
    FOR SELECT USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
    );

-- Stores can create pending relationships with creators
CREATE POLICY "Stores can create pending relationships with creators" ON store_creator_relationships
    FOR INSERT WITH CHECK (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid() AND user_type = 'store') AND
        status = 'pending'
    );

-- Creators can update status of relationships where they are the creator
CREATE POLICY "Creators can update relationship status" ON store_creator_relationships
    FOR UPDATE USING (
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid() AND user_type = 'creator')
    ) WITH CHECK (
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid() AND user_type = 'creator')
    );

-- Stores can update their own relationships (for deactivation)
CREATE POLICY "Stores can update their own relationships" ON store_creator_relationships
    FOR UPDATE USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid() AND user_type = 'store')
    ) WITH CHECK (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid() AND user_type = 'store')
    );

-- Users can delete their own relationships
CREATE POLICY "Users can delete their own relationships" ON store_creator_relationships
    FOR DELETE USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
    );

-- Add index for status queries
CREATE INDEX IF NOT EXISTS idx_store_creator_relationships_status 
ON store_creator_relationships(status);

-- Add index for creator pending requests
CREATE INDEX IF NOT EXISTS idx_store_creator_relationships_creator_pending 
ON store_creator_relationships(creator_id, status) 
WHERE status = 'pending';

-- Add index for approved relationships
CREATE INDEX IF NOT EXISTS idx_store_creator_relationships_approved 
ON store_creator_relationships(store_id, creator_id, status) 
WHERE status = 'approved' AND is_active = true;
