-- Add orders and store-creator relationship tables

-- Store-Creator relationships table
CREATE TABLE IF NOT EXISTS store_creator_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    creator_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    
    -- Ensure a store can only have one relationship with each creator
    UNIQUE(store_id, creator_id)
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    store_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    creator_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'received' CHECK (status IN ('received', 'in_progress', 'shipped', 'delivered', 'cancelled')),
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_offering_id UUID NOT NULL REFERENCES user_product_offerings(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add updated_at trigger for store_creator_relationships
CREATE OR REPLACE TRIGGER update_store_creator_relationships_updated_at
    BEFORE UPDATE ON store_creator_relationships
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add updated_at trigger for orders
CREATE OR REPLACE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    counter INTEGER;
BEGIN
    -- Get the current date in YYYYMMDD format
    new_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-';
    
    -- Get the count of orders created today
    SELECT COUNT(*) + 1 INTO counter
    FROM orders
    WHERE DATE(created_at) = CURRENT_DATE;
    
    -- Pad with zeros to make it 4 digits
    new_number := new_number || LPAD(counter::TEXT, 4, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate order numbers
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER set_order_number_trigger
    BEFORE INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();

-- Function to update order total when items change
CREATE OR REPLACE FUNCTION update_order_total()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE orders 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0)
        FROM order_items 
        WHERE order_id = COALESCE(NEW.order_id, OLD.order_id)
    )
    WHERE id = COALESCE(NEW.order_id, OLD.order_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Triggers to update order total when items are added/updated/deleted
CREATE OR REPLACE TRIGGER update_order_total_on_insert
    AFTER INSERT ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION update_order_total();

CREATE OR REPLACE TRIGGER update_order_total_on_update
    AFTER UPDATE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION update_order_total();

CREATE OR REPLACE TRIGGER update_order_total_on_delete
    AFTER DELETE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION update_order_total();

-- RLS Policies

-- Store-Creator relationships policies
ALTER TABLE store_creator_relationships ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own store-creator relationships" ON store_creator_relationships
    FOR SELECT USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
    );

CREATE POLICY "Stores can create relationships with creators" ON store_creator_relationships
    FOR INSERT WITH CHECK (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid() AND user_type = 'store')
    );

CREATE POLICY "Users can update their own relationships" ON store_creator_relationships
    FOR UPDATE USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
    );

CREATE POLICY "Users can delete their own relationships" ON store_creator_relationships
    FOR DELETE USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
    );

-- Orders policies
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own orders" ON orders
    FOR SELECT USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
    );

CREATE POLICY "Stores can create orders" ON orders
    FOR INSERT WITH CHECK (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid() AND user_type = 'store')
    );

CREATE POLICY "Users can update their own orders" ON orders
    FOR UPDATE USING (
        store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
        creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
    );

-- Order items policies
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view order items for their orders" ON order_items
    FOR SELECT USING (
        order_id IN (
            SELECT id FROM orders WHERE 
            store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
            creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
        )
    );

CREATE POLICY "Users can manage order items for their orders" ON order_items
    FOR ALL USING (
        order_id IN (
            SELECT id FROM orders WHERE 
            store_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid()) OR
            creator_id IN (SELECT id FROM user_profiles WHERE user_id = auth.uid())
        )
    );

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_store_creator_relationships_store_id ON store_creator_relationships(store_id);
CREATE INDEX IF NOT EXISTS idx_store_creator_relationships_creator_id ON store_creator_relationships(creator_id);
CREATE INDEX IF NOT EXISTS idx_orders_store_id ON orders(store_id);
CREATE INDEX IF NOT EXISTS idx_orders_creator_id ON orders(creator_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_offering_id ON order_items(product_offering_id);
