create type "public"."relationship_status" as enum ('pending', 'approved', 'rejected');

create table "public"."order_items" (
    "id" uuid not null default gen_random_uuid(),
    "order_id" uuid not null,
    "product_offering_id" uuid not null,
    "quantity" integer not null,
    "unit_price" numeric(10,2) not null,
    "total_price" numeric(10,2) not null,
    "created_at" timestamp with time zone default now()
);


alter table "public"."order_items" enable row level security;

create table "public"."orders" (
    "id" uuid not null default gen_random_uuid(),
    "order_number" character varying(50) not null,
    "store_id" uuid not null,
    "creator_id" uuid not null,
    "status" character varying(20) not null default 'received'::character varying,
    "total_amount" numeric(10,2) not null default 0,
    "notes" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."orders" enable row level security;

create table "public"."store_creator_relationships" (
    "id" uuid not null default gen_random_uuid(),
    "store_id" uuid not null,
    "creator_id" uuid not null,
    "status" relationship_status default 'pending'::relationship_status,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "is_active" boolean default false
);


alter table "public"."store_creator_relationships" enable row level security;

CREATE INDEX idx_order_items_order_id ON public.order_items USING btree (order_id);

CREATE INDEX idx_order_items_product_offering_id ON public.order_items USING btree (product_offering_id);

CREATE INDEX idx_orders_created_at ON public.orders USING btree (created_at);

CREATE INDEX idx_orders_creator_id ON public.orders USING btree (creator_id);

CREATE INDEX idx_orders_status ON public.orders USING btree (status);

CREATE INDEX idx_orders_store_id ON public.orders USING btree (store_id);

CREATE INDEX idx_store_creator_relationships_approved ON public.store_creator_relationships USING btree (store_id, creator_id, status) WHERE ((status = 'approved'::relationship_status) AND (is_active = true));

CREATE INDEX idx_store_creator_relationships_creator_id ON public.store_creator_relationships USING btree (creator_id);

CREATE INDEX idx_store_creator_relationships_creator_pending ON public.store_creator_relationships USING btree (creator_id, status) WHERE (status = 'pending'::relationship_status);

CREATE INDEX idx_store_creator_relationships_status ON public.store_creator_relationships USING btree (status);

CREATE INDEX idx_store_creator_relationships_store_id ON public.store_creator_relationships USING btree (store_id);

CREATE UNIQUE INDEX order_items_pkey ON public.order_items USING btree (id);

CREATE UNIQUE INDEX orders_order_number_key ON public.orders USING btree (order_number);

CREATE UNIQUE INDEX orders_pkey ON public.orders USING btree (id);

CREATE UNIQUE INDEX store_creator_relationships_pkey ON public.store_creator_relationships USING btree (id);

CREATE UNIQUE INDEX store_creator_relationships_store_id_creator_id_key ON public.store_creator_relationships USING btree (store_id, creator_id);

alter table "public"."order_items" add constraint "order_items_pkey" PRIMARY KEY using index "order_items_pkey";

alter table "public"."orders" add constraint "orders_pkey" PRIMARY KEY using index "orders_pkey";

alter table "public"."store_creator_relationships" add constraint "store_creator_relationships_pkey" PRIMARY KEY using index "store_creator_relationships_pkey";

alter table "public"."order_items" add constraint "order_items_order_id_fkey" FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE not valid;

alter table "public"."order_items" validate constraint "order_items_order_id_fkey";

alter table "public"."order_items" add constraint "order_items_product_offering_id_fkey" FOREIGN KEY (product_offering_id) REFERENCES user_product_offerings(id) ON DELETE CASCADE not valid;

alter table "public"."order_items" validate constraint "order_items_product_offering_id_fkey";

alter table "public"."order_items" add constraint "order_items_quantity_check" CHECK ((quantity > 0)) not valid;

alter table "public"."order_items" validate constraint "order_items_quantity_check";

alter table "public"."orders" add constraint "orders_creator_id_fkey" FOREIGN KEY (creator_id) REFERENCES user_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."orders" validate constraint "orders_creator_id_fkey";

alter table "public"."orders" add constraint "orders_order_number_key" UNIQUE using index "orders_order_number_key";

alter table "public"."orders" add constraint "orders_status_check" CHECK (((status)::text = ANY ((ARRAY['received'::character varying, 'in_progress'::character varying, 'shipped'::character varying, 'delivered'::character varying, 'cancelled'::character varying])::text[]))) not valid;

alter table "public"."orders" validate constraint "orders_status_check";

alter table "public"."orders" add constraint "orders_store_id_fkey" FOREIGN KEY (store_id) REFERENCES user_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."orders" validate constraint "orders_store_id_fkey";

alter table "public"."store_creator_relationships" add constraint "store_creator_relationships_creator_id_fkey" FOREIGN KEY (creator_id) REFERENCES user_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."store_creator_relationships" validate constraint "store_creator_relationships_creator_id_fkey";

alter table "public"."store_creator_relationships" add constraint "store_creator_relationships_store_id_creator_id_key" UNIQUE using index "store_creator_relationships_store_id_creator_id_key";

alter table "public"."store_creator_relationships" add constraint "store_creator_relationships_store_id_fkey" FOREIGN KEY (store_id) REFERENCES user_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."store_creator_relationships" validate constraint "store_creator_relationships_store_id_fkey";

grant delete on table "public"."order_items" to "anon";

grant insert on table "public"."order_items" to "anon";

grant references on table "public"."order_items" to "anon";

grant select on table "public"."order_items" to "anon";

grant trigger on table "public"."order_items" to "anon";

grant truncate on table "public"."order_items" to "anon";

grant update on table "public"."order_items" to "anon";

grant delete on table "public"."order_items" to "authenticated";

grant insert on table "public"."order_items" to "authenticated";

grant references on table "public"."order_items" to "authenticated";

grant select on table "public"."order_items" to "authenticated";

grant trigger on table "public"."order_items" to "authenticated";

grant truncate on table "public"."order_items" to "authenticated";

grant update on table "public"."order_items" to "authenticated";

grant delete on table "public"."order_items" to "service_role";

grant insert on table "public"."order_items" to "service_role";

grant references on table "public"."order_items" to "service_role";

grant select on table "public"."order_items" to "service_role";

grant trigger on table "public"."order_items" to "service_role";

grant truncate on table "public"."order_items" to "service_role";

grant update on table "public"."order_items" to "service_role";

grant delete on table "public"."orders" to "anon";

grant insert on table "public"."orders" to "anon";

grant references on table "public"."orders" to "anon";

grant select on table "public"."orders" to "anon";

grant trigger on table "public"."orders" to "anon";

grant truncate on table "public"."orders" to "anon";

grant update on table "public"."orders" to "anon";

grant delete on table "public"."orders" to "authenticated";

grant insert on table "public"."orders" to "authenticated";

grant references on table "public"."orders" to "authenticated";

grant select on table "public"."orders" to "authenticated";

grant trigger on table "public"."orders" to "authenticated";

grant truncate on table "public"."orders" to "authenticated";

grant update on table "public"."orders" to "authenticated";

grant delete on table "public"."orders" to "service_role";

grant insert on table "public"."orders" to "service_role";

grant references on table "public"."orders" to "service_role";

grant select on table "public"."orders" to "service_role";

grant trigger on table "public"."orders" to "service_role";

grant truncate on table "public"."orders" to "service_role";

grant update on table "public"."orders" to "service_role";

grant delete on table "public"."store_creator_relationships" to "anon";

grant insert on table "public"."store_creator_relationships" to "anon";

grant references on table "public"."store_creator_relationships" to "anon";

grant select on table "public"."store_creator_relationships" to "anon";

grant trigger on table "public"."store_creator_relationships" to "anon";

grant truncate on table "public"."store_creator_relationships" to "anon";

grant update on table "public"."store_creator_relationships" to "anon";

grant delete on table "public"."store_creator_relationships" to "authenticated";

grant insert on table "public"."store_creator_relationships" to "authenticated";

grant references on table "public"."store_creator_relationships" to "authenticated";

grant select on table "public"."store_creator_relationships" to "authenticated";

grant trigger on table "public"."store_creator_relationships" to "authenticated";

grant truncate on table "public"."store_creator_relationships" to "authenticated";

grant update on table "public"."store_creator_relationships" to "authenticated";

grant delete on table "public"."store_creator_relationships" to "service_role";

grant insert on table "public"."store_creator_relationships" to "service_role";

grant references on table "public"."store_creator_relationships" to "service_role";

grant select on table "public"."store_creator_relationships" to "service_role";

grant trigger on table "public"."store_creator_relationships" to "service_role";

grant truncate on table "public"."store_creator_relationships" to "service_role";

grant update on table "public"."store_creator_relationships" to "service_role";

create policy "Stores can create order items"
on "public"."order_items"
as permissive
for insert
to public
with check ((order_id IN ( SELECT orders.id
   FROM orders
  WHERE (orders.store_id IN ( SELECT user_profiles.id
           FROM user_profiles
          WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'store'::user_type_enum)))))));


create policy "Users can delete their own order items"
on "public"."order_items"
as permissive
for delete
to public
using ((order_id IN ( SELECT orders.id
   FROM orders
  WHERE ((orders.store_id IN ( SELECT user_profiles.id
           FROM user_profiles
          WHERE (user_profiles.user_id = auth.uid()))) OR (orders.creator_id IN ( SELECT user_profiles.id
           FROM user_profiles
          WHERE (user_profiles.user_id = auth.uid())))))));


create policy "Users can update their own order items"
on "public"."order_items"
as permissive
for update
to public
using ((order_id IN ( SELECT orders.id
   FROM orders
  WHERE ((orders.store_id IN ( SELECT user_profiles.id
           FROM user_profiles
          WHERE (user_profiles.user_id = auth.uid()))) OR (orders.creator_id IN ( SELECT user_profiles.id
           FROM user_profiles
          WHERE (user_profiles.user_id = auth.uid())))))));


create policy "Users can view their own order items"
on "public"."order_items"
as permissive
for select
to public
using ((order_id IN ( SELECT orders.id
   FROM orders
  WHERE ((orders.store_id IN ( SELECT user_profiles.id
           FROM user_profiles
          WHERE (user_profiles.user_id = auth.uid()))) OR (orders.creator_id IN ( SELECT user_profiles.id
           FROM user_profiles
          WHERE (user_profiles.user_id = auth.uid())))))));


create policy "Stores can create orders"
on "public"."orders"
as permissive
for insert
to public
with check ((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'store'::user_type_enum)))));


create policy "Users can update their own orders"
on "public"."orders"
as permissive
for update
to public
using (((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid()))) OR (creator_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid())))));


create policy "Users can view their own orders"
on "public"."orders"
as permissive
for select
to public
using (((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid()))) OR (creator_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid())))));


create policy "Creators can update relationship status"
on "public"."store_creator_relationships"
as permissive
for update
to public
using ((creator_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))))
with check ((creator_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))));


create policy "Stores can create pending relationships with creators"
on "public"."store_creator_relationships"
as permissive
for insert
to public
with check (((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'store'::user_type_enum)))) AND (status = 'pending'::relationship_status)));


create policy "Stores can update their own relationships"
on "public"."store_creator_relationships"
as permissive
for update
to public
using ((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'store'::user_type_enum)))))
with check ((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'store'::user_type_enum)))));


create policy "Users can delete their own relationships"
on "public"."store_creator_relationships"
as permissive
for delete
to public
using (((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid()))) OR (creator_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid())))));


create policy "Users can view their own store-creator relationships"
on "public"."store_creator_relationships"
as permissive
for select
to public
using (((store_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid()))) OR (creator_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE (user_profiles.user_id = auth.uid())))));


CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON public.orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_store_creator_relationships_updated_at BEFORE UPDATE ON public.store_creator_relationships FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


