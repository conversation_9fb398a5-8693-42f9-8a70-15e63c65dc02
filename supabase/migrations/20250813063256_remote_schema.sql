create table "public"."user_product_offerings" (
    "id" uuid not null default uuid_generate_v4(),
    "user_profile_id" uuid not null,
    "product_id" uuid not null,
    "stock_quantity" integer not null default 0,
    "min_stock_level" integer not null default 0,
    "is_available" boolean not null default true,
    "is_featured" boolean not null default false,
    "custom_price" numeric(10,2),
    "creator_notes" text,
    "display_order" integer default 0,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."user_product_offerings" enable row level security;

alter table "public"."product_types" enable row level security;

alter table "public"."products" enable row level security;

alter table "public"."user_profiles" add column "store_name_uri" text;

CREATE INDEX idx_user_product_offerings_available ON public.user_product_offerings USING btree (is_available) WHERE (is_available = true);

CREATE INDEX idx_user_product_offerings_display_order ON public.user_product_offerings USING btree (user_profile_id, display_order);

CREATE INDEX idx_user_product_offerings_featured ON public.user_product_offerings USING btree (is_featured) WHERE (is_featured = true);

CREATE INDEX idx_user_product_offerings_product_id ON public.user_product_offerings USING btree (product_id);

CREATE INDEX idx_user_product_offerings_user_profile_id ON public.user_product_offerings USING btree (user_profile_id);

CREATE UNIQUE INDEX user_product_offerings_pkey ON public.user_product_offerings USING btree (id);

CREATE UNIQUE INDEX user_product_offerings_user_profile_id_product_id_key ON public.user_product_offerings USING btree (user_profile_id, product_id);

CREATE UNIQUE INDEX user_profiles_store_name_uri_key ON public.user_profiles USING btree (store_name_uri);

alter table "public"."user_product_offerings" add constraint "user_product_offerings_pkey" PRIMARY KEY using index "user_product_offerings_pkey";

alter table "public"."user_product_offerings" add constraint "user_product_offerings_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE not valid;

alter table "public"."user_product_offerings" validate constraint "user_product_offerings_product_id_fkey";

alter table "public"."user_product_offerings" add constraint "user_product_offerings_user_profile_id_fkey" FOREIGN KEY (user_profile_id) REFERENCES user_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."user_product_offerings" validate constraint "user_product_offerings_user_profile_id_fkey";

alter table "public"."user_product_offerings" add constraint "user_product_offerings_user_profile_id_product_id_key" UNIQUE using index "user_product_offerings_user_profile_id_product_id_key";

alter table "public"."user_profiles" add constraint "user_profiles_store_name_uri_check" CHECK ((length(store_name_uri) < 50)) not valid;

alter table "public"."user_profiles" validate constraint "user_profiles_store_name_uri_check";

alter table "public"."user_profiles" add constraint "user_profiles_store_name_uri_key" UNIQUE using index "user_profiles_store_name_uri_key";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.check_product_availability(creator_id_param uuid, product_id_param uuid)
 RETURNS TABLE(is_available boolean, stock_quantity integer, effective_price numeric, stock_status text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        upo.is_available,
        upo.stock_quantity,
        COALESCE(upo.custom_price, pt.price) as effective_price,
        CASE 
            WHEN upo.stock_quantity <= 0 THEN 'out_of_stock'
            WHEN upo.stock_quantity <= upo.min_stock_level THEN 'low_stock'
            ELSE 'in_stock'
        END as stock_status
    FROM user_product_offerings upo
    JOIN products p ON upo.product_id = p.id
    JOIN product_types pt ON p.product_type_id = pt.id
    WHERE upo.user_profile_id = creator_id_param 
      AND upo.product_id = product_id_param
      AND upo.is_available = true
      AND p.active = true;
END;
$function$
;

create or replace view "public"."creator_product_offerings" as  SELECT upo.id AS offering_id,
    upo.stock_quantity,
    upo.min_stock_level,
    upo.is_available,
    upo.is_featured,
    upo.custom_price,
    upo.creator_notes,
    upo.display_order,
    upo.created_at AS offering_created_at,
    upo.updated_at AS offering_updated_at,
    p.id AS product_id,
    p.sku,
    p.color,
    p.scent,
    p.active AS product_active,
    pt.id AS product_type_id,
    pt.name AS product_name,
    pt.description AS product_description,
    pt.weight_grams,
    pt.price AS base_price,
    COALESCE(upo.custom_price, pt.price) AS effective_price,
    up.id AS creator_id,
    up.store_name AS creator_name,
    up.store_logo_url AS creator_logo_url,
    up.user_id AS creator_user_id,
        CASE
            WHEN (upo.stock_quantity <= 0) THEN 'out_of_stock'::text
            WHEN (upo.stock_quantity <= upo.min_stock_level) THEN 'low_stock'::text
            ELSE 'in_stock'::text
        END AS stock_status,
        CASE
            WHEN (NOT upo.is_available) THEN 'discontinued'::text
            WHEN (NOT p.active) THEN 'product_inactive'::text
            WHEN (upo.stock_quantity <= 0) THEN 'out_of_stock'::text
            ELSE 'available'::text
        END AS availability_status
   FROM (((user_product_offerings upo
     JOIN products p ON ((upo.product_id = p.id)))
     JOIN product_types pt ON ((p.product_type_id = pt.id)))
     JOIN user_profiles up ON ((upo.user_profile_id = up.id)))
  WHERE (up.user_type = 'creator'::user_type_enum)
  ORDER BY up.store_name, upo.display_order, pt.name;


CREATE OR REPLACE FUNCTION public.get_current_user_product_offerings()
 RETURNS TABLE(offering_id uuid, product_id uuid, product_name text, product_description text, sku text, color text, scent text, base_price numeric, custom_price numeric, effective_price numeric, stock_quantity integer, min_stock_level integer, is_available boolean, is_featured boolean, creator_notes text, display_order integer, stock_status text, availability_status text, weight_grams integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        cpo.offering_id,
        cpo.product_id,
        cpo.product_name,
        cpo.product_description,
        cpo.sku,
        cpo.color,
        cpo.scent,
        cpo.base_price,
        cpo.custom_price,
        cpo.effective_price,
        cpo.stock_quantity,
        cpo.min_stock_level,
        cpo.is_available,
        cpo.is_featured,
        cpo.creator_notes,
        cpo.display_order,
        cpo.stock_status,
        cpo.availability_status,
        cpo.weight_grams
    FROM creator_product_offerings cpo
    WHERE cpo.creator_user_id = auth.uid()
    ORDER BY cpo.display_order, cpo.product_name;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_featured_products(limit_param integer DEFAULT 10)
 RETURNS TABLE(offering_id uuid, creator_name text, creator_logo_url text, product_name text, product_description text, color text, scent text, effective_price numeric, stock_status text, weight_grams integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        ppl.offering_id,
        ppl.creator_name,
        ppl.creator_logo_url,
        ppl.product_name,
        ppl.product_description,
        ppl.color,
        ppl.scent,
        ppl.effective_price,
        ppl.stock_status,
        ppl.weight_grams
    FROM public_product_listings ppl
    WHERE ppl.is_featured = true
      AND ppl.availability_status = 'available'
    ORDER BY ppl.creator_name, ppl.product_name
    LIMIT limit_param;
END;
$function$
;

create or replace view "public"."public_product_listings" as  SELECT upo.id AS offering_id,
    upo.stock_quantity,
    upo.min_stock_level,
    upo.is_available,
    upo.is_featured,
    upo.custom_price,
    upo.display_order,
    upo.created_at AS offering_created_at,
    upo.updated_at AS offering_updated_at,
    p.id AS product_id,
    p.sku,
    p.color,
    p.scent,
    p.active AS product_active,
    pt.id AS product_type_id,
    pt.name AS product_name,
    pt.description AS product_description,
    pt.weight_grams,
    pt.price AS base_price,
    COALESCE(upo.custom_price, pt.price) AS effective_price,
    up.id AS creator_id,
    up.store_name AS creator_name,
    up.store_logo_url AS creator_logo_url,
    up.contact_email AS creator_contact_email,
        CASE
            WHEN (upo.stock_quantity <= 0) THEN 'out_of_stock'::text
            WHEN (upo.stock_quantity <= upo.min_stock_level) THEN 'low_stock'::text
            ELSE 'in_stock'::text
        END AS stock_status,
        CASE
            WHEN (NOT upo.is_available) THEN 'discontinued'::text
            WHEN (NOT p.active) THEN 'product_inactive'::text
            WHEN (upo.stock_quantity <= 0) THEN 'out_of_stock'::text
            ELSE 'available'::text
        END AS availability_status
   FROM (((user_product_offerings upo
     JOIN products p ON ((upo.product_id = p.id)))
     JOIN product_types pt ON ((p.product_type_id = pt.id)))
     JOIN user_profiles up ON ((upo.user_profile_id = up.id)))
  WHERE ((upo.is_available = true) AND (up.is_active = true) AND (up.user_type = 'creator'::user_type_enum) AND (p.active = true))
  ORDER BY upo.is_featured DESC, up.store_name, upo.display_order, pt.name;


CREATE OR REPLACE FUNCTION public.toggle_offering_availability(offering_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    user_profile_id_var UUID;
    current_availability BOOLEAN;
BEGIN
    -- Check if the offering belongs to the current user and get current availability
    SELECT upo.user_profile_id, upo.is_available 
    INTO user_profile_id_var, current_availability
    FROM user_product_offerings upo
    JOIN user_profiles up ON upo.user_profile_id = up.id
    WHERE upo.id = offering_id_param AND up.user_id = auth.uid();
    
    IF user_profile_id_var IS NULL THEN
        RETURN FALSE; -- User doesn't own this offering
    END IF;
    
    -- Toggle availability
    UPDATE user_product_offerings
    SET is_available = NOT current_availability,
        updated_at = NOW()
    WHERE id = offering_id_param;
    
    RETURN TRUE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_offering_stock(offering_id_param uuid, new_quantity integer)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    user_profile_id_var UUID;
BEGIN
    -- Check if the offering belongs to the current user
    SELECT user_profile_id INTO user_profile_id_var
    FROM user_product_offerings upo
    JOIN user_profiles up ON upo.user_profile_id = up.id
    WHERE upo.id = offering_id_param AND up.user_id = auth.uid();
    
    IF user_profile_id_var IS NULL THEN
        RETURN FALSE; -- User doesn't own this offering
    END IF;
    
    -- Update the stock quantity
    UPDATE user_product_offerings
    SET stock_quantity = new_quantity,
        updated_at = NOW()
    WHERE id = offering_id_param;
    
    RETURN TRUE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_user_product_offerings_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$function$
;

grant delete on table "public"."user_product_offerings" to "anon";

grant insert on table "public"."user_product_offerings" to "anon";

grant references on table "public"."user_product_offerings" to "anon";

grant select on table "public"."user_product_offerings" to "anon";

grant trigger on table "public"."user_product_offerings" to "anon";

grant truncate on table "public"."user_product_offerings" to "anon";

grant update on table "public"."user_product_offerings" to "anon";

grant delete on table "public"."user_product_offerings" to "authenticated";

grant insert on table "public"."user_product_offerings" to "authenticated";

grant references on table "public"."user_product_offerings" to "authenticated";

grant select on table "public"."user_product_offerings" to "authenticated";

grant trigger on table "public"."user_product_offerings" to "authenticated";

grant truncate on table "public"."user_product_offerings" to "authenticated";

grant update on table "public"."user_product_offerings" to "authenticated";

grant delete on table "public"."user_product_offerings" to "service_role";

grant insert on table "public"."user_product_offerings" to "service_role";

grant references on table "public"."user_product_offerings" to "service_role";

grant select on table "public"."user_product_offerings" to "service_role";

grant trigger on table "public"."user_product_offerings" to "service_role";

grant truncate on table "public"."user_product_offerings" to "service_role";

grant update on table "public"."user_product_offerings" to "service_role";

create policy "Public read access to product types"
on "public"."product_types"
as permissive
for select
to public
using (true);


create policy "Creators can view all products"
on "public"."products"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))));


create policy "Public read access to active products"
on "public"."products"
as permissive
for select
to public
using ((active = true));


create policy "Creators can delete their own offerings"
on "public"."user_product_offerings"
as permissive
for delete
to public
using ((user_profile_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))));


create policy "Creators can insert their own offerings"
on "public"."user_product_offerings"
as permissive
for insert
to public
with check ((user_profile_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))));


create policy "Creators can update their own offerings"
on "public"."user_product_offerings"
as permissive
for update
to public
using ((user_profile_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))))
with check ((user_profile_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))));


create policy "Creators can view their own offerings"
on "public"."user_product_offerings"
as permissive
for select
to public
using ((user_profile_id IN ( SELECT user_profiles.id
   FROM user_profiles
  WHERE ((user_profiles.user_id = auth.uid()) AND (user_profiles.user_type = 'creator'::user_type_enum)))));


create policy "Public read access to available offerings"
on "public"."user_product_offerings"
as permissive
for select
to public
using ((is_available = true));


CREATE TRIGGER trigger_update_user_product_offerings_updated_at BEFORE UPDATE ON public.user_product_offerings FOR EACH ROW EXECUTE FUNCTION update_user_product_offerings_updated_at();


