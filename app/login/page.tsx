import { login, signup } from "./actions";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, CheckCircle } from "lucide-react";
import Link from "next/link";

interface LoginPageProps {
  searchParams: Promise<{
    error?: string;
    message?: string;
  }>;
}

const errorMessages = {
  "missing-credentials": "Please enter both email and password.",
  "invalid-email": "Please enter a valid email address.",
  "invalid-credentials":
    "Invalid email or password. Please check your credentials and try again.",
  "email-not-confirmed":
    "Please check your email and click the confirmation link before signing in.",
  "too-many-requests":
    "Too many login attempts. Please wait a moment before trying again.",
  "user-not-found": "No account found with this email address.",
  "password-too-short": "Password must be at least 6 characters long.",
  "user-already-exists":
    "An account with this email already exists. Please sign in instead.",
  "signup-disabled": "New user registration is currently disabled.",
  "confirmation-failed":
    "Email confirmation failed. Please try requesting a new confirmation email.",
  "invalid-confirmation-link":
    "Invalid confirmation link. Please check your email for the correct link.",
  "unknown-error": "An unexpected error occurred. Please try again.",
};

const successMessages = {
  "check-email":
    "Check your email for a confirmation link to complete your account setup.",
};

export default async function LoginPage({ searchParams }: LoginPageProps) {
  const { error, message } = await searchParams;

  return (
    <div className="container mx-auto flex items-center justify-center min-h-[calc(100vh-4rem)] py-6">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Welcome to Planty Invoice</CardTitle>
          <CardDescription>
            Sign in to your account to manage your invoices
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {errorMessages[error as keyof typeof errorMessages] ||
                  errorMessages["unknown-error"]}
              </AlertDescription>
            </Alert>
          )}

          {message && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                {successMessages[message as keyof typeof successMessages]}
              </AlertDescription>
            </Alert>
          )}

          <form className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email"
                required
                autoComplete="email"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="Enter your password"
                required
                autoComplete="current-password"
              />
            </div>

            <div className="space-y-2">
              <Button formAction={login} className="w-full">
                Sign In
              </Button>
              <Button formAction={signup} variant="outline" className="w-full">
                Create Account
              </Button>
            </div>
          </form>

          <div className="text-center text-sm text-muted-foreground">
            <Link href="/" className="hover:underline">
              ← Back to Home
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
