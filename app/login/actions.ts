'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'

export async function login(formData: FormData) {
  const supabase = await createClient()

  // Type-casting here for convenience
  // In practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  // Validate inputs
  if (!data.email || !data.password) {
    redirect('/login?error=missing-credentials')
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(data.email)) {
    redirect('/login?error=invalid-email')
  }

  const { error } = await supabase.auth.signInWithPassword(data)

  if (error) {
    // Handle different types of authentication errors
    let errorCode = 'unknown-error'
    
    if (error.message.includes('Invalid login credentials')) {
      errorCode = 'invalid-credentials'
    } else if (error.message.includes('Email not confirmed')) {
      errorCode = 'email-not-confirmed'
    } else if (error.message.includes('Too many requests')) {
      errorCode = 'too-many-requests'
    } else if (error.message.includes('User not found')) {
      errorCode = 'user-not-found'
    } else if (error.message.includes('Invalid email')) {
      errorCode = 'invalid-email'
    } else if (error.message.includes('Password should be at least')) {
      errorCode = 'password-too-short'
    }

    redirect(`/login?error=${errorCode}`)
  }

  revalidatePath('/', 'layout')
  redirect('/')
}

export async function signup(formData: FormData) {
  const supabase = await createClient()

  // Type-casting here for convenience
  // In practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  // Validate inputs
  if (!data.email || !data.password) {
    redirect('/login?error=missing-credentials')
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(data.email)) {
    redirect('/login?error=invalid-email')
  }

  // Validate password strength
  if (data.password.length < 6) {
    redirect('/login?error=password-too-short')
  }

  const { error } = await supabase.auth.signUp(data)

  if (error) {
    // Handle different types of signup errors
    let errorCode = 'unknown-error'
    
    if (error.message.includes('User already registered')) {
      errorCode = 'user-already-exists'
    } else if (error.message.includes('Password should be at least')) {
      errorCode = 'password-too-short'
    } else if (error.message.includes('Invalid email')) {
      errorCode = 'invalid-email'
    } else if (error.message.includes('Signup is disabled')) {
      errorCode = 'signup-disabled'
    } else if (error.message.includes('Too many requests')) {
      errorCode = 'too-many-requests'
    }

    redirect(`/login?error=${errorCode}`)
  }

  revalidatePath('/', 'layout')
  redirect('/login?message=check-email')
}
