import type { Metada<PERSON> } from "next";
import { <PERSON>eist } from "next/font/google";
import { ThemeProvider } from "next-themes";
import {
  NavigationWrapper,
  MobileNavigationWrapper,
} from "@/components/navigation-wrapper";
import { Toaster } from "@/components/ui/toaster";
import "./globals.css";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  metadataBase: new URL(defaultUrl),
  title: "Planty Invoice - Invoice Management System",
  description: "Simple and efficient invoice management for small businesses",
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen flex flex-col">
            <NavigationWrapper />
            <main className="flex-1 pb-16 md:pb-0">{children}</main>
            <MobileNavigationWrapper />
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
