"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
// import { InvoiceList } from "@/components/invoices/invoice-list";
// import { InvoiceForm } from "@/components/invoices/invoice-form";
// import { CustomerForm } from "@/components/customers/customer-form";
// import { ProductTypeForm } from "@/components/products/product-type-form";
// import { ProductForm } from "@/components/products/product-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Invoice,
  InvoiceFormData,
  CreateCustomerData,
  CreateProductTypeData,
  CreateProductData,
} from "@/lib/types/database";
import {
  createInvoiceAction,
  createCustomerAction,
  createProductTypeAction,
  createProductAction,
} from "./actions";
import { ArrowLeft } from "lucide-react";

type ViewMode =
  | "list"
  | "create-invoice"
  | "create-customer"
  | "create-product-type"
  | "create-product";

export function InvoicesPageClient() {
  const router = useRouter();
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleCreateInvoice = async (data: InvoiceFormData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createInvoiceAction(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Invoice created successfully!");
        setViewMode("list");
      }
    } catch (err) {
      setError("Failed to create invoice. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateCustomer = async (data: CreateCustomerData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createCustomerAction(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Customer created successfully!");
        setViewMode("list");
      }
    } catch (err) {
      setError("Failed to create customer. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProductType = async (data: CreateProductTypeData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createProductTypeAction(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Product type created successfully!");
        setViewMode("list");
      }
    } catch (err) {
      setError("Failed to create product type. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProduct = async (data: CreateProductData) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await createProductAction(data);
      if (result.error) {
        setError(result.error);
      } else {
        setSuccess("Product created successfully!");
        setViewMode("list");
      }
    } catch (err) {
      setError("Failed to create product. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const renderContent = () => {
    switch (viewMode) {
      case "create-invoice":
        return (
          <Card>
            <CardHeader>
              <CardTitle>Create Invoice</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Invoice creation form will be implemented here.
              </p>
              <Button
                onClick={() => setViewMode("list")}
                variant="outline"
                className="mt-4"
              >
                Back to List
              </Button>
            </CardContent>
          </Card>
        );
      case "create-customer":
        return (
          <Card>
            <CardHeader>
              <CardTitle>Add Customer</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Customer creation form will be implemented here.
              </p>
              <Button
                onClick={() => setViewMode("list")}
                variant="outline"
                className="mt-4"
              >
                Back to List
              </Button>
            </CardContent>
          </Card>
        );
      case "create-product-type":
        return (
          <Card>
            <CardHeader>
              <CardTitle>Add Product Type</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Product type creation form will be implemented here.
              </p>
              <Button
                onClick={() => setViewMode("list")}
                variant="outline"
                className="mt-4"
              >
                Back to List
              </Button>
            </CardContent>
          </Card>
        );
      case "create-product":
        return (
          <Card>
            <CardHeader>
              <CardTitle>Add Product</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Product creation form will be implemented here.
              </p>
              <Button
                onClick={() => setViewMode("list")}
                variant="outline"
                className="mt-4"
              >
                Back to List
              </Button>
            </CardContent>
          </Card>
        );
      default:
        return (
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="invoices" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="invoices">Invoices</TabsTrigger>
                    <TabsTrigger value="customers">Customers</TabsTrigger>
                    <TabsTrigger value="products">Products</TabsTrigger>
                  </TabsList>
                  <TabsContent value="invoices" className="space-y-4">
                    <Button
                      onClick={() => setViewMode("create-invoice")}
                      className="w-full"
                    >
                      Create New Invoice
                    </Button>
                  </TabsContent>
                  <TabsContent value="customers" className="space-y-4">
                    <Button
                      onClick={() => setViewMode("create-customer")}
                      className="w-full"
                    >
                      Add New Customer
                    </Button>
                  </TabsContent>
                  <TabsContent value="products" className="space-y-4">
                    <div className="space-y-2">
                      <Button
                        onClick={() => setViewMode("create-product-type")}
                        className="w-full"
                        variant="outline"
                      >
                        Add Product Type
                      </Button>
                      <Button
                        onClick={() => setViewMode("create-product")}
                        className="w-full"
                      >
                        Add New Product
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Invoice List Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>Invoices</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Invoice list will be implemented here. You are successfully
                  authenticated!
                </p>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {viewMode === "list" && "Invoice Management"}
            {viewMode === "create-invoice" && "Create New Invoice"}
            {viewMode === "create-customer" && "Add New Customer"}
            {viewMode === "create-product-type" && "Add Product Type"}
            {viewMode === "create-product" && "Add New Product"}
          </h1>
          <p className="text-muted-foreground">
            {viewMode === "list" &&
              "Manage your invoices, customers, and products"}
            {viewMode === "create-invoice" &&
              "Fill out the form below to create a new invoice"}
            {viewMode === "create-customer" &&
              "Add a new customer to your database"}
            {viewMode === "create-product-type" &&
              "Create a new product category"}
            {viewMode === "create-product" &&
              "Add a new product to your inventory"}
          </p>
        </div>
        {viewMode !== "list" && (
          <Button
            variant="outline"
            onClick={() => setViewMode("list")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to List
          </Button>
        )}
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      {success && (
        <div className="bg-green-100 text-green-800 px-4 py-3 rounded-md">
          {success}
        </div>
      )}

      {/* Content */}
      {renderContent()}
    </div>
  );
}
