import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { getCurrentUserProfile } from "@/lib/services/user-profiles";
import { CreatorDashboard } from "./creator-dashboard";
import { StoreAccessDenied } from "./store-access-denied";
import { ProfileSetup } from "./profile-setup";

export default async function ManageStorePage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  // Get user profile to check user type
  const profileResult = await getCurrentUserProfile();

  if (!profileResult.success || !profileResult.data) {
    // If no profile exists, show profile creation flow
    return <ProfileSetup user={user} />;
  }

  const userProfile = profileResult.data;

  // Check if user is a creator
  if (userProfile.user_type !== "creator") {
    return <StoreAccessDenied userType={userProfile.user_type} />;
  }

  return <CreatorDashboard userProfile={userProfile} />;
}
