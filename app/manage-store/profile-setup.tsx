"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Store } from "lucide-react";

import { useToast } from "@/hooks/use-toast";
import { createProfileAction } from "./actions";

interface ProfileSetupProps {
  user: {
    id: string;
    email?: string;
  };
}

export function ProfileSetup({ user }: ProfileSetupProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();

  // Form state
  const [storeName, setStoreName] = useState("");
  const [userType, setUserType] = useState<"creator" | "store">("creator");
  const [contactEmail, setContactEmail] = useState(user.email || "");
  const [contact<PERSON><PERSON>, set<PERSON>ontact<PERSON><PERSON>] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted with data:", {
      storeName,
      userType,
      contactEmail,
      contactPhone,
    });

    if (!storeName.trim()) {
      toast({
        title: "Validation Error",
        description: "Store name is required",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("store_name", storeName.trim());
      formData.append("user_type", userType);
      formData.append("contact_email", contactEmail.trim());
      formData.append("contact_phone", contactPhone.trim());

      const result = await createProfileAction(formData);

      if (result.success) {
        toast({
          title: "Success",
          description: "Profile created successfully! Welcome to Planty.",
        });
        // Force a complete page reload to show the dashboard
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to create profile",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Store className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle className="text-2xl">Welcome to Planty!</CardTitle>
            <p className="text-muted-foreground">
              Let&apos;s set up your profile to get started with managing your
              business.
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* User Type Selection */}
              <div className="space-y-3">
                <Label>I am a...</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      userType === "creator"
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => setUserType("creator")}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="userType"
                        value="creator"
                        checked={userType === "creator"}
                        onChange={() => setUserType("creator")}
                        className="text-blue-600"
                      />
                      <div>
                        <div className="font-semibold">Creator</div>
                        <div className="text-sm text-muted-foreground">
                          I make and sell products
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      userType === "store"
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => setUserType("store")}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="userType"
                        value="store"
                        checked={userType === "store"}
                        onChange={() => setUserType("store")}
                        className="text-blue-600"
                      />
                      <div>
                        <div className="font-semibold">Store</div>
                        <div className="text-sm text-muted-foreground">
                          I buy and resell products
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Store Name */}
              <div className="space-y-2">
                <Label htmlFor="store_name">
                  {userType === "creator" ? "Business Name" : "Store Name"} *
                </Label>
                <Input
                  id="store_name"
                  value={storeName}
                  onChange={(e) => setStoreName(e.target.value)}
                  placeholder={
                    userType === "creator"
                      ? "e.g., Artisan Soaps Co."
                      : "e.g., Downtown Boutique"
                  }
                  required
                />
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contact_email">
                    Contact Email (optional)
                  </Label>
                  <Input
                    id="contact_email"
                    type="email"
                    value={contactEmail}
                    onChange={(e) => setContactEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                  <p className="text-xs text-muted-foreground">
                    This doesn&apos;t need to match your login email. Use a
                    separate email if you prefer to track customer messages
                    separately.
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact_phone">
                    Contact Phone (optional)
                  </Label>
                  <Input
                    id="contact_phone"
                    type="tel"
                    value={contactPhone}
                    onChange={(e) => setContactPhone(e.target.value)}
                    placeholder="+****************"
                  />
                </div>
              </div>

              {/* Information Box */}
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-semibold mb-2">What happens next?</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  {userType === "creator" ? (
                    <>
                      <li>
                        • Access the creator dashboard to manage your products
                      </li>
                      <li>• Add your product offerings with custom pricing</li>
                      <li>• Set stock levels and availability</li>
                      <li>• Start receiving orders from stores</li>
                    </>
                  ) : (
                    <>
                      <li>• Browse available products from creators</li>
                      <li>• Place orders for your store</li>
                      <li>• Manage your store locations</li>
                      <li>• Track your order history</li>
                    </>
                  )}
                </ul>
              </div>

              {/* Submit Button */}
              <Button
                type="button"
                className="w-full"
                disabled={isSubmitting}
                onClick={async (e) => {
                  console.log("Button clicked");
                  alert("Button clicked!");
                  await handleSubmit(e as any);
                }}
              >
                {isSubmitting && (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                )}
                Create Profile & Get Started
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
