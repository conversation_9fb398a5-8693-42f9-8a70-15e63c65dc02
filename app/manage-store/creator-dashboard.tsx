import { Suspense } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Plus,
  Package,
  AlertTriangle,
  ShoppingCart,
  Users,
  Settings,
} from "lucide-react";
import { UserProfile } from "@/lib/types/user-profiles";
import { getCurrentUserProductOfferings } from "@/lib/services/product-offerings";
import { ProductOfferingsTable } from "./components/product-offerings-table";
import { AddOfferingDialog } from "./components/add-offering-dialog";
import { DashboardStats } from "./components/dashboard-stats";
import { OrdersTable } from "./components/orders-table";
import { StoreOrdersTable } from "./components/store-orders-table";
import { CreatorRelationshipsTable } from "./components/creator-relationships-table";
import { StoreManagement } from "./components/store-management";

interface CreatorDashboardProps {
  userProfile: UserProfile;
}

export async function CreatorDashboard({ userProfile }: CreatorDashboardProps) {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Manage Store</h1>
          <p className="text-muted-foreground">
            Welcome back, {userProfile.store_name}!
            {userProfile.user_type === "creator"
              ? " Manage your product offerings and orders."
              : " Manage your orders and creator relationships."}
          </p>
        </div>
      </div>

      {/* Tabbed Interface */}
      <Tabs
        defaultValue={
          userProfile.user_type === "creator" ? "orders" : "my-orders"
        }
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3">
          {userProfile.user_type === "creator" ? (
            <>
              <TabsTrigger value="orders" className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Orders
              </TabsTrigger>
              <TabsTrigger value="products" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Product Offerings
              </TabsTrigger>
            </>
          ) : (
            <>
              <TabsTrigger
                value="my-orders"
                className="flex items-center gap-2"
              >
                <ShoppingCart className="h-4 w-4" />
                My Orders
              </TabsTrigger>
              <TabsTrigger
                value="store-management"
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Store Management
              </TabsTrigger>
              <TabsTrigger value="creators" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                My Creators
              </TabsTrigger>
            </>
          )}
        </TabsList>

        {/* Creator Tabs */}
        {userProfile.user_type === "creator" && (
          <>
            <TabsContent value="orders" className="space-y-6">
              <Suspense fallback={<div>Loading orders...</div>}>
                <OrdersTable />
              </Suspense>
            </TabsContent>

            <TabsContent value="products" className="space-y-6">
              {/* Dashboard Stats */}
              <Suspense fallback={<DashboardStatsSkeleton />}>
                <DashboardStatsWrapper />
              </Suspense>

              {/* Add Product Button */}
              <div className="flex justify-end">
                <AddOfferingDialog>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Product
                  </Button>
                </AddOfferingDialog>
              </div>

              {/* Product Offerings Table */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Your Product Offerings
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Suspense fallback={<ProductOfferingsTableSkeleton />}>
                    <ProductOfferingsTableWrapper />
                  </Suspense>
                </CardContent>
              </Card>
            </TabsContent>
          </>
        )}

        {/* Store Tabs */}
        {userProfile.user_type === "store" && (
          <>
            <TabsContent value="my-orders" className="space-y-6">
              <Suspense fallback={<div>Loading orders...</div>}>
                <StoreOrdersTable />
              </Suspense>
            </TabsContent>

            <TabsContent value="store-management" className="space-y-6">
              <StoreManagement userProfile={userProfile} />
            </TabsContent>

            <TabsContent value="creators" className="space-y-6">
              <Suspense fallback={<div>Loading creator relationships...</div>}>
                <CreatorRelationshipsTable />
              </Suspense>
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  );
}

// Wrapper component to handle async data fetching for stats
async function DashboardStatsWrapper() {
  const result = await getCurrentUserProductOfferings();

  if (result.error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span>Failed to load dashboard stats: {result.error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return <DashboardStats offerings={result.data || []} />;
}

// Wrapper component to handle async data fetching for table
async function ProductOfferingsTableWrapper() {
  const result = await getCurrentUserProductOfferings();

  if (result.error) {
    return (
      <div className="flex items-center gap-2 text-destructive p-4">
        <AlertTriangle className="h-4 w-4" />
        <span>Failed to load product offerings: {result.error}</span>
      </div>
    );
  }

  return <ProductOfferingsTable offerings={result.data || []} />;
}

// Loading skeletons
function DashboardStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
            <div className="h-4 w-4 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent>
            <div className="h-8 w-16 bg-muted animate-pulse rounded mb-1" />
            <div className="h-3 w-24 bg-muted animate-pulse rounded" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function ProductOfferingsTableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="h-10 w-full bg-muted animate-pulse rounded" />
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-16 w-full bg-muted animate-pulse rounded" />
      ))}
    </div>
  );
}
