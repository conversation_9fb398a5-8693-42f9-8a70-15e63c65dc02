# **MANAGE STORE PRD**

## **Overview**

The "Manage Store" feature is a central hub for creators to manage their product offerings, inventory, pricing, and availability. It provides a user-friendly interface for creators to add, edit, and delete product offerings, as well as manage stock levels and availability.

## **How it Works**

### Profile setup

1. **User visits `/manage-store`** - If the user is not authenticated, they are redirected to the login page
2. **After login** - If the user has not created a profile, they are shown the profile setup form
3. **User fills form** - The user enters their store name, user type, contact email, and contact phone. The form indicates that the contact email doesn't necessarily need to match their login email, in case they prefer to keep track of customer messages separately. The store name and user type are mandatory fields; the rest are optional. The form clearly indicates so.
4. **Profile created** - The user's profile is created with the provided information
5. **Dashboard loads** - The user is redirected to the manage store dashboard

### Page

When users land in the page after setting up their profile, their experience will vary depending on whether they are a creator or a store.

If they are a store, they will be shown three tabs:

- One for viewing the list of orders they have made so far, with their status – and the creator they placed the order with.
- One for managing their store, including their store name, logo, contact email, and contact phone. They can also add and manage their store locations from here.
- One for tracking the creators they work with, and adding new creators to work with.

Within the tab to see the list of orders, they will have a button to "Place a new order". When they click on this button, they will be taken to a page where they can select one of the stores they work with. From there, they can browse the products available from that store and place an order to the store.

Within the tab to track creators, they will have a button to "Work with a new creator". When they click on this button, they will be shown an input field to include the name of the creator they want to work with. If this name doesn't exist as a creator in the DB, they will be shown an error message letting them know that there is no current creator with that name, and to reach out to the creator directly to set up a store account or request clarification from them.

If they are a creator, they will be shown two tabs:

- One for managing their orders
- One for managing their product offerings

Within the tab to manage their orders, they can:

- See a list of all orders they have received so far, with the store that placed the order, and the date it was made.
- Update individual orders to their new status (Received, In progress, Shipped, Delivered, Cancelled)

Within the tab to manage their product offerings, they can:

- Create new products (SKU) and variations of them (color, scent, etc.)
- Edit product and variation details
- Delete products and variations
- Add or remove stock to products
