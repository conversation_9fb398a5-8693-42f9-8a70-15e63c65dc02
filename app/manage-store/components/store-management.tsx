"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { Settings, Store, MapPin, Plus, Loader2 } from "lucide-react";
import { UserProfile } from "@/lib/types/user-profiles";
import { useToast } from "@/hooks/use-toast";

interface StoreManagementProps {
  userProfile: UserProfile;
}

export function StoreManagement({ userProfile }: StoreManagementProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Form state
  const [storeName, setStoreName] = useState(userProfile.store_name);
  const [contactEmail, setContactEmail] = useState(
    userProfile.contact_email || ""
  );
  const [contactP<PERSON>, setContactPhone] = useState(
    userProfile.contact_phone || ""
  );

  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // TODO: Implement profile update functionality
      toast({
        title: "Success",
        description: "Store information updated successfully",
      });
      setIsEditing(false);
    } catch {
      toast({
        title: "Error",
        description: "Failed to update store information",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setStoreName(userProfile.store_name);
    setContactEmail(userProfile.contact_email || "");
    setContactPhone(userProfile.contact_phone || "");
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* Store Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Store className="h-5 w-5" />
              Store Information
            </CardTitle>
            {!isEditing ? (
              <Button variant="outline" onClick={() => setIsEditing(true)}>
                <Settings className="h-4 w-4 mr-2" />
                Edit
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button onClick={handleSave} disabled={isSubmitting}>
                  {isSubmitting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Save Changes
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="store_name">Store Name</Label>
              {isEditing ? (
                <Input
                  id="store_name"
                  value={storeName}
                  onChange={(e) => setStoreName(e.target.value)}
                  placeholder="Your store name"
                />
              ) : (
                <div className="p-2 bg-muted rounded-md">
                  {userProfile.store_name}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label>User Type</Label>
              <div className="p-2 bg-muted rounded-md capitalize">
                {userProfile.user_type}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contact_email">Contact Email</Label>
              {isEditing ? (
                <Input
                  id="contact_email"
                  type="email"
                  value={contactEmail}
                  onChange={(e) => setContactEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              ) : (
                <div className="p-2 bg-muted rounded-md">
                  {userProfile.contact_email || "Not provided"}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="contact_phone">Contact Phone</Label>
              {isEditing ? (
                <Input
                  id="contact_phone"
                  type="tel"
                  value={contactPhone}
                  onChange={(e) => setContactPhone(e.target.value)}
                  placeholder="+****************"
                />
              ) : (
                <div className="p-2 bg-muted rounded-md">
                  {userProfile.contact_phone || "Not provided"}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Store Locations */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Store Locations
            </CardTitle>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Location
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Store Locations</h3>
            <p className="text-muted-foreground mb-4">
              You haven&apos;t added any store locations yet.
            </p>
            <p className="text-sm text-muted-foreground mb-6">
              Add your store locations to help creators understand your business
              better.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Location
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Store Logo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            Store Logo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="w-24 h-24 mx-auto bg-muted rounded-lg flex items-center justify-center mb-4">
              <Store className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No Logo Uploaded</h3>
            <p className="text-muted-foreground mb-4">
              Upload your store logo to personalize your profile.
            </p>
            <Button variant="outline">Upload Logo</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
