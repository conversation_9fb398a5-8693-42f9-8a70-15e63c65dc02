"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { getAvailableProducts } from "@/lib/services/product-offerings";
import { createOfferingAction } from "../actions";
import { useToast } from "@/hooks/use-toast";
import { Product, ProductType } from "@/lib/types/product-offerings";

interface AddOfferingDialogProps {
  children: React.ReactNode;
}

type ProductWithType = Product & { product_type: ProductType };

export function AddOfferingDialog({ children }: AddOfferingDialogProps) {
  const [open, setOpen] = useState(false);
  const [products, setProducts] = useState<ProductWithType[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Form state
  const [selectedProduct, setSelectedProduct] = useState<string>("");
  const [stockQuantity, setStockQuantity] = useState<string>("0");
  const [minStockLevel, setMinStockLevel] = useState<string>("0");
  const [customPrice, setCustomPrice] = useState<string>("");
  const [isAvailable, setIsAvailable] = useState(true);
  const [isFeatured, setIsFeatured] = useState(false);
  const [creatorNotes, setCreatorNotes] = useState("");
  const [displayOrder, setDisplayOrder] = useState<string>("0");

  const loadProducts = useCallback(async () => {
    setIsLoadingProducts(true);
    try {
      const result = await getAvailableProducts();
      if (result.error) {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
      } else {
        setProducts(result.data || []);
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to load products",
        variant: "destructive",
      });
    } finally {
      setIsLoadingProducts(false);
    }
  }, [toast]);

  // Load products when dialog opens
  useEffect(() => {
    if (open) {
      loadProducts();
    }
  }, [open, loadProducts]);

  const resetForm = () => {
    setSelectedProduct("");
    setStockQuantity("0");
    setMinStockLevel("0");
    setCustomPrice("");
    setIsAvailable(true);
    setIsFeatured(false);
    setCreatorNotes("");
    setDisplayOrder("0");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProduct) {
      toast({
        title: "Validation Error",
        description: "Please select a product",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("product_id", selectedProduct);
      formData.append("stock_quantity", stockQuantity);
      formData.append("min_stock_level", minStockLevel);
      formData.append("custom_price", customPrice);
      formData.append("is_available", isAvailable.toString());
      formData.append("is_featured", isFeatured.toString());
      formData.append("creator_notes", creatorNotes);
      formData.append("display_order", displayOrder);

      const result = await createOfferingAction(formData);

      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        resetForm();
        setOpen(false);
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to create offering",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProductData = products.find((p) => p.id === selectedProduct);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Product Offering</DialogTitle>
          <DialogDescription>
            Add a new product to your store catalog with custom pricing and
            inventory settings.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Product Selection */}
          <div className="space-y-2">
            <Label htmlFor="product">Product *</Label>
            {isLoadingProducts ? (
              <div className="flex items-center gap-2 p-3 border rounded-md">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading products...</span>
              </div>
            ) : (
              <Select
                value={selectedProduct}
                onValueChange={setSelectedProduct}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a product" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      <div className="flex flex-col">
                        <span>{product.product_type.name}</span>
                        <span className="text-sm text-muted-foreground">
                          {[product.color, product.scent]
                            .filter(Boolean)
                            .join(" • ")}
                          {product.sku && ` • SKU: ${product.sku}`}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {selectedProductData && (
              <div className="text-sm text-muted-foreground p-3 bg-muted rounded-md">
                <div>
                  <strong>Base Price:</strong> $
                  {selectedProductData.product_type.price.toFixed(2)}
                </div>
                {selectedProductData.product_type.description && (
                  <div>
                    <strong>Description:</strong>{" "}
                    {selectedProductData.product_type.description}
                  </div>
                )}
                {selectedProductData.product_type.weight_grams && (
                  <div>
                    <strong>Weight:</strong>{" "}
                    {selectedProductData.product_type.weight_grams}g
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Stock Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stock_quantity">Initial Stock Quantity *</Label>
              <Input
                id="stock_quantity"
                type="number"
                min="0"
                value={stockQuantity}
                onChange={(e) => setStockQuantity(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="min_stock_level">Minimum Stock Level</Label>
              <Input
                id="min_stock_level"
                type="number"
                min="0"
                value={minStockLevel}
                onChange={(e) => setMinStockLevel(e.target.value)}
              />
            </div>
          </div>

          {/* Pricing */}
          <div className="space-y-2">
            <Label htmlFor="custom_price">Custom Price (optional)</Label>
            <Input
              id="custom_price"
              type="number"
              step="0.01"
              min="0"
              value={customPrice}
              onChange={(e) => setCustomPrice(e.target.value)}
              placeholder={
                selectedProductData
                  ? `Base price: $${selectedProductData.product_type.price.toFixed(
                      2
                    )}`
                  : "Leave empty to use base price"
              }
            />
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_available"
                checked={isAvailable}
                onCheckedChange={(checked) =>
                  setIsAvailable(checked as boolean)
                }
              />
              <Label htmlFor="is_available">Available for purchase</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_featured"
                checked={isFeatured}
                onCheckedChange={(checked) => setIsFeatured(checked as boolean)}
              />
              <Label htmlFor="is_featured">Featured product</Label>
            </div>
          </div>

          {/* Display Order */}
          <div className="space-y-2">
            <Label htmlFor="display_order">Display Order</Label>
            <Input
              id="display_order"
              type="number"
              min="0"
              value={displayOrder}
              onChange={(e) => setDisplayOrder(e.target.value)}
              placeholder="0"
            />
          </div>

          {/* Creator Notes */}
          <div className="space-y-2">
            <Label htmlFor="creator_notes">Internal Notes (optional)</Label>
            <Textarea
              id="creator_notes"
              value={creatorNotes}
              onChange={(e) => setCreatorNotes(e.target.value)}
              placeholder="Add any internal notes about this product offering..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Add Product
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
