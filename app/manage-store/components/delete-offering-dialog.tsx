"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Loader2 } from "lucide-react";
import { deleteOfferingAction } from "../actions";
import { useToast } from "@/hooks/use-toast";
import { GetCurrentUserProductOfferingsResult } from "@/lib/types/product-offerings";

interface DeleteOfferingDialogProps {
  offering: GetCurrentUserProductOfferingsResult;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeleteOfferingDialog({
  offering,
  open,
  onOpenChange,
}: DeleteOfferingDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      const result = await deleteOfferingAction(offering.offering_id);

      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to delete offering",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Product Offering</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the offering for{" "}
            <strong>{offering.product_name}</strong>
            {(offering.color || offering.scent) && (
              <span>
                {" "}
                ({[offering.color, offering.scent].filter(Boolean).join(" • ")})
              </span>
            )}
            ?
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="p-4 bg-muted rounded-lg">
          <div className="text-sm space-y-1">
            <div>
              <strong>Current Stock:</strong> {offering.stock_quantity} units
            </div>
            <div>
              <strong>Stock Value:</strong> $
              {(offering.stock_quantity * offering.effective_price).toFixed(2)}
            </div>
            {offering.creator_notes && (
              <div>
                <strong>Notes:</strong> {offering.creator_notes}
              </div>
            )}
          </div>
        </div>

        <div className="text-sm text-muted-foreground">
          <strong>Warning:</strong> This action cannot be undone. The product
          offering will be permanently removed from your store catalog. Consider
          discontinuing the product instead if you might want to offer it again
          in the future.
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Delete Offering
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
