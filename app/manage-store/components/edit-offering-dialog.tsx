"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2 } from "lucide-react";
import { updateOfferingAction } from "../actions";
import { useToast } from "@/hooks/use-toast";
import { GetCurrentUserProductOfferingsResult } from "@/lib/types/product-offerings";

interface EditOfferingDialogProps {
  offering: GetCurrentUserProductOfferingsResult;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditOfferingDialog({
  offering,
  open,
  onOpenChange,
}: EditOfferingDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Form state
  const [stockQuantity, setStockQuantity] = useState<string>(
    offering.stock_quantity.toString()
  );
  const [minStockLevel, setMinStockLevel] = useState<string>(
    offering.min_stock_level.toString()
  );
  const [customPrice, setCustomPrice] = useState<string>(
    offering.custom_price?.toString() || ""
  );
  const [isAvailable, setIsAvailable] = useState(offering.is_available);
  const [isFeatured, setIsFeatured] = useState(offering.is_featured);
  const [creatorNotes, setCreatorNotes] = useState(
    offering.creator_notes || ""
  );
  const [displayOrder, setDisplayOrder] = useState<string>(
    offering.display_order.toString()
  );

  // Reset form when offering changes
  useEffect(() => {
    setStockQuantity(offering.stock_quantity.toString());
    setMinStockLevel(offering.min_stock_level.toString());
    setCustomPrice(offering.custom_price?.toString() || "");
    setIsAvailable(offering.is_available);
    setIsFeatured(offering.is_featured);
    setCreatorNotes(offering.creator_notes || "");
    setDisplayOrder(offering.display_order.toString());
  }, [offering]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("stock_quantity", stockQuantity);
      formData.append("min_stock_level", minStockLevel);
      formData.append("custom_price", customPrice);
      formData.append("is_available", isAvailable.toString());
      formData.append("is_featured", isFeatured.toString());
      formData.append("creator_notes", creatorNotes);
      formData.append("display_order", displayOrder);

      const result = await updateOfferingAction(offering.offering_id, formData);

      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        onOpenChange(false);
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to update offering",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Product Offering</DialogTitle>
          <DialogDescription>
            Update the settings for {offering.product_name}
          </DialogDescription>
        </DialogHeader>

        {/* Product Info */}
        <div className="p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">{offering.product_name}</h4>
          <div className="text-sm text-muted-foreground space-y-1">
            {offering.sku && (
              <div>
                <strong>SKU:</strong> {offering.sku}
              </div>
            )}
            {(offering.color || offering.scent) && (
              <div>
                <strong>Variant:</strong>{" "}
                {[offering.color, offering.scent].filter(Boolean).join(" • ")}
              </div>
            )}
            <div>
              <strong>Base Price:</strong> ${offering.base_price.toFixed(2)}
            </div>
            {offering.weight_grams && (
              <div>
                <strong>Weight:</strong> {offering.weight_grams}g
              </div>
            )}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Stock Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stock_quantity">Stock Quantity *</Label>
              <Input
                id="stock_quantity"
                type="number"
                min="0"
                value={stockQuantity}
                onChange={(e) => setStockQuantity(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="min_stock_level">Minimum Stock Level</Label>
              <Input
                id="min_stock_level"
                type="number"
                min="0"
                value={minStockLevel}
                onChange={(e) => setMinStockLevel(e.target.value)}
              />
            </div>
          </div>

          {/* Pricing */}
          <div className="space-y-2">
            <Label htmlFor="custom_price">Custom Price (optional)</Label>
            <Input
              id="custom_price"
              type="number"
              step="0.01"
              min="0"
              value={customPrice}
              onChange={(e) => setCustomPrice(e.target.value)}
              placeholder={`Base price: $${offering.base_price.toFixed(2)}`}
            />
            <div className="text-sm text-muted-foreground">
              Current effective price: ${offering.effective_price.toFixed(2)}
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_available"
                checked={isAvailable}
                onCheckedChange={(checked) =>
                  setIsAvailable(checked as boolean)
                }
              />
              <Label htmlFor="is_available">Available for purchase</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_featured"
                checked={isFeatured}
                onCheckedChange={(checked) => setIsFeatured(checked as boolean)}
              />
              <Label htmlFor="is_featured">Featured product</Label>
            </div>
          </div>

          {/* Display Order */}
          <div className="space-y-2">
            <Label htmlFor="display_order">Display Order</Label>
            <Input
              id="display_order"
              type="number"
              min="0"
              value={displayOrder}
              onChange={(e) => setDisplayOrder(e.target.value)}
            />
          </div>

          {/* Creator Notes */}
          <div className="space-y-2">
            <Label htmlFor="creator_notes">Internal Notes (optional)</Label>
            <Textarea
              id="creator_notes"
              value={creatorNotes}
              onChange={(e) => setCreatorNotes(e.target.value)}
              placeholder="Add any internal notes about this product offering..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Update Product
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
