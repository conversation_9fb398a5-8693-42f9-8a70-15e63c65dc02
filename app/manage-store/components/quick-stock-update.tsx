"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, X, Edit3 } from "lucide-react";
import { updateStockAction } from "../actions";
import { useToast } from "@/hooks/use-toast";

interface QuickStockUpdateProps {
  offeringId: string;
  currentStock: number;
}

export function QuickStockUpdate({
  offeringId,
  currentStock,
}: QuickStockUpdateProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [stockValue, setStockValue] = useState(currentStock.toString());
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSave = async () => {
    const newStock = parseInt(stockValue);

    if (isNaN(newStock) || newStock < 0) {
      toast({
        title: "Invalid Stock",
        description: "Please enter a valid stock quantity (0 or greater)",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await updateStockAction(offeringId, newStock);

      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        setIsEditing(false);
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
        // Reset to current value on error
        setStockValue(currentStock.toString());
      }
    } catch {
      toast({
        title: "Error",
        description: "Failed to update stock",
        variant: "destructive",
      });
      setStockValue(currentStock.toString());
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setStockValue(currentStock.toString());
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsEditing(true)}
        className="h-6 px-2 text-xs"
      >
        <Edit3 className="h-3 w-3 mr-1" />
        Quick Edit
      </Button>
    );
  }

  return (
    <div className="flex items-center gap-1">
      <Input
        type="number"
        value={stockValue}
        onChange={(e) => setStockValue(e.target.value)}
        className="h-6 w-16 text-xs"
        min="0"
        disabled={isLoading}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            handleSave();
          } else if (e.key === "Escape") {
            handleCancel();
          }
        }}
        autoFocus
      />
      <Button
        variant="ghost"
        size="sm"
        onClick={handleSave}
        disabled={isLoading}
        className="h-6 w-6 p-0"
      >
        <Check className="h-3 w-3 text-green-600" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleCancel}
        disabled={isLoading}
        className="h-6 w-6 p-0"
      >
        <X className="h-3 w-3 text-red-600" />
      </Button>
    </div>
  );
}
