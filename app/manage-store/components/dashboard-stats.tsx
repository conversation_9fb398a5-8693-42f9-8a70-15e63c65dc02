import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Package, TrendingUp, Al<PERSON><PERSON>riangle, Star } from "lucide-react";
import { GetCurrentUserProductOfferingsResult } from "@/lib/types/product-offerings";

interface DashboardStatsProps {
  offerings: GetCurrentUserProductOfferingsResult[];
}

export function DashboardStats({ offerings }: DashboardStatsProps) {
  // Calculate stats from offerings
  const totalOfferings = offerings.length;
  const availableOfferings = offerings.filter(o => o.is_available).length;
  const featuredOfferings = offerings.filter(o => o.is_featured).length;
  const lowStockOfferings = offerings.filter(o => 
    o.stock_status === 'low_stock' || o.stock_status === 'out_of_stock'
  ).length;
  const totalStockValue = offerings.reduce((sum, offering) => {
    return sum + (offering.stock_quantity * offering.effective_price);
  }, 0);

  const stats = [
    {
      title: "Total Products",
      value: totalOfferings,
      description: `${availableOfferings} available`,
      icon: Package,
      trend: availableOfferings > 0 ? "positive" : "neutral",
    },
    {
      title: "Featured Items",
      value: featuredOfferings,
      description: "Highlighted products",
      icon: Star,
      trend: featuredOfferings > 0 ? "positive" : "neutral",
    },
    {
      title: "Stock Value",
      value: `$${totalStockValue.toFixed(2)}`,
      description: "Total inventory value",
      icon: TrendingUp,
      trend: totalStockValue > 0 ? "positive" : "neutral",
    },
    {
      title: "Stock Alerts",
      value: lowStockOfferings,
      description: "Low/out of stock",
      icon: AlertTriangle,
      trend: lowStockOfferings > 0 ? "warning" : "positive",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-4 w-4 ${
              stat.trend === "positive" 
                ? "text-green-600" 
                : stat.trend === "warning" 
                ? "text-yellow-600" 
                : "text-muted-foreground"
            }`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
