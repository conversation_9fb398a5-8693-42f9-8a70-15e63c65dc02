"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Star,
  StarOff,
  Package,
} from "lucide-react";
import { GetCurrentUserProductOfferingsResult } from "@/lib/types/product-offerings";
import { EditOfferingDialog } from "./edit-offering-dialog";
import { DeleteOfferingDialog } from "./delete-offering-dialog";
import { QuickStockUpdate } from "./quick-stock-update";
import { toggleAvailabilityAction } from "../actions";
import { useToast } from "@/hooks/use-toast";

interface ProductOfferingsTableProps {
  offerings: GetCurrentUserProductOfferingsResult[];
}

export function ProductOfferingsTable({
  offerings,
}: ProductOfferingsTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [editingOffering, setEditingOffering] =
    useState<GetCurrentUserProductOfferingsResult | null>(null);
  const [deletingOffering, setDeletingOffering] =
    useState<GetCurrentUserProductOfferingsResult | null>(null);
  const { toast } = useToast();

  // Filter offerings based on search term
  const filteredOfferings = offerings.filter(
    (offering) =>
      offering.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      offering.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      offering.color?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      offering.scent?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggleAvailability = async (offeringId: string) => {
    const result = await toggleAvailabilityAction(offeringId);

    if (result.success) {
      toast({
        title: "Success",
        description: result.message,
      });
    } else {
      toast({
        title: "Error",
        description: result.error,
        variant: "destructive",
      });
    }
  };

  const getStockStatusBadge = (stockStatus: string, stockQuantity: number) => {
    switch (stockStatus) {
      case "out_of_stock":
        return <Badge variant="destructive">Out of Stock</Badge>;
      case "low_stock":
        return <Badge variant="secondary">Low Stock ({stockQuantity})</Badge>;
      case "in_stock":
        return <Badge variant="default">In Stock ({stockQuantity})</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getAvailabilityBadge = (
    availabilityStatus: string,
    isAvailable: boolean
  ) => {
    if (!isAvailable) {
      return <Badge variant="outline">Discontinued</Badge>;
    }

    switch (availabilityStatus) {
      case "available":
        return <Badge variant="default">Available</Badge>;
      case "out_of_stock":
        return <Badge variant="secondary">Out of Stock</Badge>;
      case "product_inactive":
        return <Badge variant="destructive">Product Inactive</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  if (offerings.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Product Offerings</h3>
        <p className="text-muted-foreground mb-4">
          You haven&apos;t added any products to your store yet.
        </p>
        <p className="text-sm text-muted-foreground">
          Click &quot;Add Product&quot; to start building your product catalog.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="flex items-center gap-4">
        <Input
          placeholder="Search products..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
        <div className="text-sm text-muted-foreground">
          {filteredOfferings.length} of {offerings.length} products
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>SKU</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Stock</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Featured</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOfferings.map((offering) => (
              <TableRow key={offering.offering_id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{offering.product_name}</div>
                    <div className="text-sm text-muted-foreground">
                      {[offering.color, offering.scent]
                        .filter(Boolean)
                        .join(" • ")}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <code className="text-sm">{offering.sku || "—"}</code>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      ${offering.effective_price.toFixed(2)}
                    </div>
                    {offering.custom_price && (
                      <div className="text-sm text-muted-foreground">
                        Base: ${offering.base_price.toFixed(2)}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {getStockStatusBadge(
                      offering.stock_status,
                      offering.stock_quantity
                    )}
                    <QuickStockUpdate
                      offeringId={offering.offering_id}
                      currentStock={offering.stock_quantity}
                    />
                  </div>
                </TableCell>
                <TableCell>
                  {getAvailabilityBadge(
                    offering.availability_status,
                    offering.is_available
                  )}
                </TableCell>
                <TableCell>
                  {offering.is_featured ? (
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  ) : (
                    <StarOff className="h-4 w-4 text-muted-foreground" />
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => setEditingOffering(offering)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          handleToggleAvailability(offering.offering_id)
                        }
                      >
                        {offering.is_available ? (
                          <>
                            <EyeOff className="h-4 w-4 mr-2" />
                            Discontinue
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-2" />
                            Make Available
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setDeletingOffering(offering)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Dialogs */}
      {editingOffering && (
        <EditOfferingDialog
          offering={editingOffering}
          open={!!editingOffering}
          onOpenChange={(open) => !open && setEditingOffering(null)}
        />
      )}

      {deletingOffering && (
        <DeleteOfferingDialog
          offering={deletingOffering}
          open={!!deletingOffering}
          onOpenChange={(open) => !open && setDeletingOffering(null)}
        />
      )}
    </div>
  );
}
